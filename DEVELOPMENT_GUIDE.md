# ITS Discovery - Development Guide

## Development Environment Setup

### Prerequisites
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Git**: For version control
- **VS Code**: Recommended IDE with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter

### Initial Setup
```bash
# Clone the repository
git clone https://github.com/ITS-DEV-LG/ITS-DISCO.git
cd ITS-DISCO

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:8080`

## Project Architecture

### Technology Stack Details

#### Frontend Framework
- **React 18.3.1**: Modern React with hooks and concurrent features
- **TypeScript 5.5.3**: Type safety and enhanced developer experience
- **Vite 6.3.5**: Fast build tool and development server

#### UI Framework
- **Tailwind CSS 3.4.11**: Utility-first CSS framework
- **Radix UI**: Accessible, unstyled UI components
- **Lucide React**: Modern icon library
- **Sonner**: Toast notifications

#### State Management
- **React Context**: Global state management
- **TanStack Query 5.56.2**: Server state management and caching
- **React Hook Form 7.53.0**: Form state management

#### Backend & Database
- **Supabase**: PostgreSQL database with real-time features
- **Supabase Auth**: Authentication and user management
- **Row Level Security**: Database-level security

### Code Organization

#### Component Structure
```
components/
├── auth/              # Authentication components
│   ├── GoogleAuth.tsx
│   └── AuthGuard.tsx
├── categories/        # Product category management
│   └── CategoryManager.tsx
├── cost/              # Cost calculation components
│   ├── CostStructureManager.tsx
│   └── CostBreakdown.tsx
├── integrations/      # External service integrations
│   ├── ZapierIntegration.tsx
│   └── WeQuoteConfig.tsx
├── products/          # Product catalog components
│   └── ProductCatalog.tsx
├── rooms/             # Room configuration components
│   ├── RoomManager.tsx
│   └── SystemSelector.tsx
├── steps/             # Walkthrough step components
│   ├── ClientInfoStep.tsx
│   ├── QualificationStep.tsx
│   └── SystemSelectionStep.tsx
└── ui/                # Base UI components (Radix-based)
    ├── button.tsx
    ├── input.tsx
    └── dialog.tsx
```

#### Service Layer
```
services/
├── costService.ts         # Cost calculation logic
├── walkthroughService.ts  # Walkthrough data management
├── zapierService.ts       # Zapier integration
├── wequoteService.ts      # WeQuote integration
└── openaiService.ts       # AI-powered features
```

#### Type Definitions
```
types/
├── formTypes.ts       # Form and walkthrough types
├── roomTypes.ts       # Room configuration types
└── userTypes.ts       # User and profile types
```

## Development Workflow

### Branch Strategy
- **main/development**: Primary development branch
- **feature/**: Feature development branches
- **hotfix/**: Critical bug fixes
- **release/**: Release preparation branches

### Code Standards

#### TypeScript Configuration
```json
{
  "compilerOptions": {
    "strict": false,           // Relaxed for rapid development
    "noImplicitAny": false,    // Allows any types temporarily
    "target": "ES2020",
    "jsx": "react-jsx"
  }
}
```

#### ESLint Configuration
- **@typescript-eslint**: TypeScript-specific linting
- **react-hooks**: React hooks linting
- **react-refresh**: Fast refresh compatibility

#### Prettier Configuration
- **2 spaces**: Indentation
- **Single quotes**: String literals
- **Trailing commas**: ES5 compatible

### Development Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Production build
npm run build:dev       # Development build
npm run preview         # Preview production build

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix auto-fixable issues
npx tsc --noEmit        # Type checking

# Dependencies
npm audit               # Security audit
npm audit fix           # Fix vulnerabilities
npm update              # Update dependencies
```

## Database Development

### Supabase Setup
1. **Project Configuration**: Located in `supabase/config.toml`
2. **Type Generation**: Auto-generated types in `src/integrations/supabase/types.ts`
3. **Client Configuration**: `src/integrations/supabase/client.ts`

### Database Schema

#### Core Tables
```sql
-- Walkthroughs table
CREATE TABLE walkthroughs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  client_name TEXT NOT NULL,
  form_data JSONB NOT NULL,
  last_step INTEGER DEFAULT 1,
  team_id UUID REFERENCES teams(id),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product catalog
CREATE TABLE product_catalog_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  sku_number TEXT,
  system_category TEXT NOT NULL,
  unit_cost DECIMAL NOT NULL,
  quantity INTEGER DEFAULT 1,
  unit TEXT DEFAULT 'piece',
  -- Additional fields...
);
```

#### Row Level Security (RLS)
```sql
-- Enable RLS on walkthroughs
ALTER TABLE walkthroughs ENABLE ROW LEVEL SECURITY;

-- Policy for team-based access
CREATE POLICY "Users can access team walkthroughs" ON walkthroughs
  FOR ALL USING (team_id = auth.jwt() ->> 'team_id');
```

## Component Development

### Form Components
The application uses a multi-step form pattern with React Context:

```typescript
// Form Context Provider
export const FormProvider: React.FC<FormProviderProps> = ({ children }) => {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  
  const nextStep = () => {
    setFormData(prev => ({ ...prev, currentStep: prev.currentStep + 1 }));
  };
  
  return (
    <FormContext.Provider value={{ formData, setFormData, nextStep }}>
      {children}
    </FormContext.Provider>
  );
};
```

### Step Components
Each walkthrough step is a separate component:

```typescript
const ClientInfoStep: React.FC = () => {
  const { formData, setFormData } = useFormContext();
  
  return (
    <div className="space-y-6">
      <Input
        value={formData.clientInfo.firstName}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          clientInfo: { ...prev.clientInfo, firstName: e.target.value }
        }))}
      />
    </div>
  );
};
```

### Room Management
Dynamic room creation and configuration:

```typescript
const addRoom = (roomName: string, systemSettings?: Partial<Record<string, boolean>>) => {
  const newRoom: RoomConfig = {
    id: `room-${Date.now()}`,
    name: roomName,
    systemSettings: systemSettings || {}
  };
  
  setFormData(prev => ({
    ...prev,
    rooms: [...prev.rooms, newRoom]
  }));
};
```

## Integration Development

### Zapier Integration
Webhook-based integration for external data synchronization:

```typescript
export const sendToZapier = async (webhookUrl: string, payload: any) => {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
      mode: 'no-cors'
    });
    return { success: true };
  } catch (error) {
    console.error("Zapier integration error:", error);
    throw error;
  }
};
```

### Cost Calculation Service
Dynamic cost calculation based on room configurations:

```typescript
export const generateCostStructureFromRooms = (
  rooms: RoomConfig[], 
  systemCategories: SystemCategories,
  clientName: string
): CostStructure => {
  const costStructure: CostStructure = {
    id: `CS-${Math.floor(100000 + Math.random() * 900000)}`,
    name: `${clientName} Project Estimate`,
    categories: []
  };
  
  // Generate categories based on rooms and systems
  rooms.forEach(room => {
    // Create cost items for each enabled system
  });
  
  return costStructure;
};
```

## Testing Strategy

### Unit Testing (Planned)
- **Jest**: Testing framework
- **React Testing Library**: Component testing
- **MSW**: API mocking

### Integration Testing
- **Cypress**: End-to-end testing
- **Supabase Test Database**: Isolated testing environment

### Manual Testing Checklist
- [ ] User authentication flow
- [ ] Walkthrough completion
- [ ] Cost calculation accuracy
- [ ] PDF generation
- [ ] Zapier integration
- [ ] Data persistence

## Performance Optimization

### Code Splitting
```typescript
// Lazy load heavy components
const CostManager = lazy(() => import('./pages/CostManager'));
const Reports = lazy(() => import('./pages/Reports'));
```

### Bundle Analysis
```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist
```

### Performance Monitoring
- **React DevTools Profiler**: Component performance
- **Lighthouse**: Web vitals and performance metrics
- **Bundle size monitoring**: Track dependency growth

## Deployment

### Build Process
```bash
# Production build
npm run build

# Build outputs to dist/
dist/
├── index.html
├── assets/
│   ├── index-[hash].js
│   └── index-[hash].css
└── favicon.ico
```

### Environment Variables
```bash
# .env.local (not committed)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Deployment Targets
- **Vercel**: Recommended for React applications
- **Netlify**: Alternative static hosting
- **AWS S3 + CloudFront**: Enterprise deployment

## Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf node_modules/.vite
```

#### TypeScript Errors
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Restart TypeScript server in VS Code
Cmd/Ctrl + Shift + P > "TypeScript: Restart TS Server"
```

#### Supabase Connection Issues
```typescript
// Check client configuration
import { supabase } from '@/integrations/supabase/client';

// Test connection
const { data, error } = await supabase.from('walkthroughs').select('count');
```

### Debug Tools
- **React DevTools**: Component inspection
- **Supabase Dashboard**: Database queries and logs
- **Network Tab**: API request monitoring
- **Console Logs**: Application state debugging

## Contributing Guidelines

### Pull Request Process
1. Create feature branch from `development`
2. Implement changes with tests
3. Update documentation if needed
4. Submit PR with clear description
5. Code review and approval
6. Merge to `development`

### Code Review Checklist
- [ ] TypeScript types are properly defined
- [ ] Components are properly tested
- [ ] Performance impact is considered
- [ ] Security implications are reviewed
- [ ] Documentation is updated
