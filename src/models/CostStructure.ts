
export interface CostLineItem {
  id: string;
  name: string;
  category: string;
  description?: string;
  unitCost: number;
  quantity: number;
  unit: string;
  laborHours?: number;
  laborRate?: number;
  markup?: number;  // percentage markup
  discount?: number; // percentage discount
  notes?: string;
}

export interface CostCategory {
  id: string;
  name: string;
  description?: string;
  items: CostLineItem[];
}

export interface CostStructure {
  id: string;
  name: string;
  description?: string;
  categories: CostCategory[];
  taxRate: number; // percentage
  generalMarkup: number; // percentage
  createdAt: Date;
  updatedAt: Date;
}

export interface CostSummary {
  subtotal: number;
  labor: number;
  materials: number;
  tax: number;
  discount: number;
  total: number;
  breakdown: {
    categoryName: string;
    amount: number;
  }[];
}
