import { CostStructure, CostSummary } from './CostStructure';

export interface Quote {
  id: string;
  title: string;
  clientName: string;
  clientEmail?: string;
  projectType?: string;
  costStructure: CostStructure;
  costSummary: CostSummary;
  status: 'draft' | 'sent' | 'approved' | 'rejected';
  notes?: string;
  validUntil?: Date;
  createdAt: Date;
  updatedAt: Date;
  generatedBy?: string;
  documentPath?: string; // Path to generated PDF
}

export interface SavedCostStructure {
  id: string;
  name: string;
  description?: string;
  costStructure: CostStructure;
  costSummary: CostSummary;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
  isTemplate?: boolean;
}

export interface QuoteDocument {
  id: string;
  quoteId: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  createdAt: Date;
}
