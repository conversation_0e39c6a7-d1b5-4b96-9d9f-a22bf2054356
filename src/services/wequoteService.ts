
import { v4 as uuidv4 } from 'uuid';
import { WeQuotePackageItem } from './zapierService';

export type WeQuoteConfig = {
  apiKey?: string;
  baseUrl?: string;
  userId?: string;
  companyId?: string;
};

export interface WeQuoteCategory {
  id: string;
  name: string;
  description?: string;
  systemCategory?: string;
}

export interface WeQuoteTemplate {
  id: string;
  name: string;
  createdAt: string;
  lastUpdated: string;
  status: string;
  items: any[];
  price?: number;
  totalPrice?: number;
  currency?: string;
}

// Main function to fetch WeQuote templates - mock implementation
export const fetchWeQuoteTemplates = async (): Promise<WeQuoteTemplate[]> => {
  const config = await getWeQuoteConfig();
  
  if (!config.apiKey || !config.baseUrl) {
    console.error('WeQuote API not configured');
    return [];
  }

  try {
    // Mock implementation for now
    return [];
  } catch (error) {
    console.error('Error fetching WeQuote templates:', error);
    return [];
  }
};

// Function to fetch a specific template by ID - mock implementation
export const fetchWeQuoteTemplate = async (templateId: string): Promise<WeQuoteTemplate | null> => {
  const config = await getWeQuoteConfig();
  
  if (!config.apiKey || !config.baseUrl) {
    console.error('WeQuote API not configured');
    return null;
  }

  try {
    // Mock implementation for now
    return null;
  } catch (error) {
    console.error(`Error fetching WeQuote template ${templateId}:`, error);
    return null;
  }
};

// Function to fetch WeQuote categories - mock implementation
export const fetchWeQuoteCategories = async (): Promise<WeQuoteCategory[]> => {
  const config = await getWeQuoteConfig();
  
  if (!config.apiKey || !config.baseUrl) {
    console.error('WeQuote API not configured');
    return [];
  }

  try {
    // Mock implementation for now
    return [];
  } catch (error) {
    console.error('Error fetching WeQuote categories:', error);
    return [];
  }
};

// Function to create a new WeQuote product - mock implementation
export const createWeQuoteProduct = async (
  name: string,
  description: string,
  price: number,
  categoryId: string
): Promise<any> => {
  const config = await getWeQuoteConfig();
  
  if (!config.apiKey || !config.baseUrl) {
    console.error('WeQuote API not configured');
    return null;
  }

  try {
    // Mock implementation for now
    return null;
  } catch (error) {
    console.error('Error creating WeQuote product:', error);
    return null;
  }
};

// Function to get WeQuote configuration from localStorage
export const getWeQuoteConfig = async (): Promise<WeQuoteConfig> => {
  try {
    let config: WeQuoteConfig = {
      apiKey: '',
      baseUrl: '',
      userId: '',
      companyId: ''
    };
    
    // Try to get from local storage
    const storedConfig = localStorage.getItem('wequoteConfig');
    if (storedConfig) {
      try {
        config = JSON.parse(storedConfig);
      } catch (e) {
        console.error('Error parsing WeQuote config from localStorage:', e);
      }
    }
    
    return config;
  } catch (error) {
    console.error('Error getting WeQuote config:', error);
    return {};
  }
};

// Function to save WeQuote configuration to localStorage
export const saveWeQuoteConfig = async (config: WeQuoteConfig): Promise<boolean> => {
  try {
    // Save to local storage
    localStorage.setItem('wequoteConfig', JSON.stringify(config));
    return true;
  } catch (error) {
    console.error('Error saving WeQuote config:', error);
    return false;
  }
};

// Create a mock function to simulate importing a template
export const importWeQuoteTemplate = async (templateId: string): Promise<WeQuotePackageItem[]> => {
  try {
    const template = await fetchWeQuoteTemplate(templateId);
    
    if (!template) {
      throw new Error('Template not found');
    }
    
    // Convert template items to package items
    const packageItems: WeQuotePackageItem[] = template.items.map((item: any) => {
      return {
        id: uuidv4(),
        name: item.name || 'Unnamed Item',
        description: item.description || '',
        systemCategory: mapCategoryToSystemCategory(item.category),
        unitCost: item.price || 0,
        quantity: item.quantity || 1,
        unit: item.unit || 'piece'
      };
    });
    
    return packageItems;
  } catch (error) {
    console.error('Error importing WeQuote template:', error);
    return [];
  }
};

// Helper function to map WeQuote categories to system categories
const mapCategoryToSystemCategory = (category: string): string => {
  const lowerCategory = (category || '').toLowerCase();
  
  if (lowerCategory.includes('light')) return 'lighting';
  if (lowerCategory.includes('shade') || lowerCategory.includes('blind')) return 'shades';
  if (lowerCategory.includes('audio') || lowerCategory.includes('sound')) return 'audio';
  if (lowerCategory.includes('video') || lowerCategory.includes('tv')) return 'video';
  if (lowerCategory.includes('network') || lowerCategory.includes('wifi')) return 'network';
  if (lowerCategory.includes('secur') || lowerCategory.includes('camera')) return 'security';
  if (lowerCategory.includes('control') || lowerCategory.includes('automat')) return 'controlSystems';
  
  return 'lighting'; // default to lighting if no match
};
