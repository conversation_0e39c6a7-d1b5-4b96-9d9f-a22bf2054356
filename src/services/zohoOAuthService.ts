/**
 * Zoho OAuth 2.0 Service
 * Handles authentication flow with Zoho CRM
 */

// OAuth configuration
interface ZohoOAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string[];
}

// Token response from Zoho
interface ZohoTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  api_domain: string;
  token_type: string;
  error?: string;
}

// Store OAuth tokens in localStorage
interface ZohoOAuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
  apiDomain: string;
}

// Default OAuth configuration
const defaultConfig: ZohoOAuthConfig = {
  clientId: '',
  clientSecret: '',
  redirectUri: window.location.origin + '/settings?auth=zoho', // Redirect back to settings page
  scope: ['ZohoCRM.modules.ALL', 'ZohoCRM.settings.ALL', 'ZohoCRM.users.ALL', 'ZohoContacts.contacts.ALL']
};

// Storage key for OAuth configuration
const CONFIG_STORAGE_KEY = 'zoho_oauth_config';
const TOKENS_STORAGE_KEY = 'zoho_oauth_tokens';

// Save OAuth configuration to localStorage
export const saveZohoOAuthConfig = (config: Partial<ZohoOAuthConfig>): ZohoOAuthConfig => {
  const currentConfig = getZohoOAuthConfig();
  const newConfig = { ...currentConfig, ...config };
  localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(newConfig));
  return newConfig;
};

// Get OAuth configuration from localStorage
export const getZohoOAuthConfig = (): ZohoOAuthConfig => {
  const configStr = localStorage.getItem(CONFIG_STORAGE_KEY);
  if (configStr) {
    try {
      return JSON.parse(configStr);
    } catch (e) {
      console.error("Failed to parse Zoho OAuth config:", e);
    }
  }
  return defaultConfig;
};

// Save OAuth tokens to localStorage
export const saveZohoOAuthTokens = (tokens: ZohoOAuthTokens): void => {
  localStorage.setItem(TOKENS_STORAGE_KEY, JSON.stringify(tokens));
};

// Get OAuth tokens from localStorage
export const getZohoOAuthTokens = (): ZohoOAuthTokens | null => {
  const tokensStr = localStorage.getItem(TOKENS_STORAGE_KEY);
  if (tokensStr) {
    try {
      return JSON.parse(tokensStr);
    } catch (e) {
      console.error("Failed to parse Zoho OAuth tokens:", e);
    }
  }
  return null;
};

// Clear OAuth tokens from localStorage
export const clearZohoOAuthTokens = (): void => {
  localStorage.removeItem(TOKENS_STORAGE_KEY);
};

// Generate the authorization URL to start the OAuth flow
export const generateAuthorizationUrl = (): string => {
  const config = getZohoOAuthConfig();
  
  if (!config.clientId) {
    throw new Error('Zoho Client ID is required for OAuth authorization');
  }
  
  const params = new URLSearchParams({
    client_id: config.clientId,
    response_type: 'code',
    redirect_uri: config.redirectUri,
    scope: config.scope.join(','),
    access_type: 'offline', // To get refresh token
    prompt: 'consent' // Force consent screen to get refresh token every time
  });
  
  return `https://accounts.zoho.com/oauth/v2/auth?${params.toString()}`;
};

// Exchange authorization code for tokens
export const exchangeCodeForTokens = async (code: string): Promise<ZohoOAuthTokens> => {
  const config = getZohoOAuthConfig();
  
  if (!config.clientId || !config.clientSecret) {
    throw new Error('Zoho Client ID and Client Secret are required for token exchange');
  }
  
  const params = new URLSearchParams({
    code,
    client_id: config.clientId,
    client_secret: config.clientSecret,
    redirect_uri: config.redirectUri,
    grant_type: 'authorization_code'
  });
  
  try {
    const response = await fetch('https://accounts.zoho.com/oauth/v2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params.toString()
    });
    
    const data = await response.json() as ZohoTokenResponse;
    
    if (data.error) {
      throw new Error(`Zoho OAuth Error: ${data.error}`);
    }
    
    const tokens: ZohoOAuthTokens = {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresAt: Date.now() + (data.expires_in * 1000),
      apiDomain: data.api_domain || 'www.zohoapis.com'
    };
    
    saveZohoOAuthTokens(tokens);
    return tokens;
  } catch (error) {
    console.error('Error exchanging code for tokens:', error);
    throw error;
  }
};

// Refresh the access token using the refresh token
export const refreshAccessToken = async (): Promise<ZohoOAuthTokens> => {
  const config = getZohoOAuthConfig();
  const tokens = getZohoOAuthTokens();
  
  if (!config.clientId || !config.clientSecret) {
    throw new Error('Zoho Client ID and Client Secret are required for token refresh');
  }
  
  if (!tokens || !tokens.refreshToken) {
    throw new Error('No refresh token available');
  }
  
  const params = new URLSearchParams({
    refresh_token: tokens.refreshToken,
    client_id: config.clientId,
    client_secret: config.clientSecret,
    grant_type: 'refresh_token'
  });
  
  try {
    const response = await fetch('https://accounts.zoho.com/oauth/v2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: params.toString()
    });
    
    const data = await response.json() as ZohoTokenResponse;
    
    if (data.error) {
      throw new Error(`Zoho OAuth Error: ${data.error}`);
    }
    
    const newTokens: ZohoOAuthTokens = {
      ...tokens,
      accessToken: data.access_token,
      expiresAt: Date.now() + (data.expires_in * 1000)
    };
    
    saveZohoOAuthTokens(newTokens);
    return newTokens;
  } catch (error) {
    console.error('Error refreshing access token:', error);
    throw error;
  }
};

// Get a valid access token, refreshing if necessary
export const getValidAccessToken = async (): Promise<string> => {
  const tokens = getZohoOAuthTokens();
  
  if (!tokens) {
    throw new Error('Not authenticated with Zoho CRM');
  }
  
  // If token is expired or will expire soon (within 5 minutes)
  if (tokens.expiresAt < Date.now() + (5 * 60 * 1000)) {
    try {
      const newTokens = await refreshAccessToken();
      return newTokens.accessToken;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      clearZohoOAuthTokens();
      throw new Error('Session expired. Please authenticate again.');
    }
  }
  
  return tokens.accessToken;
};

// Check if user is authenticated with Zoho
export const isAuthenticated = (): boolean => {
  const tokens = getZohoOAuthTokens();
  return !!tokens && !!tokens.accessToken;
};

// Initialize the OAuth flow by redirecting to Zoho's authorization page
export const initiateOAuthFlow = (): void => {
  try {
    const authUrl = generateAuthorizationUrl();
    console.log("Redirecting to auth URL:", authUrl);
    window.location.href = authUrl;
  } catch (error) {
    console.error('Failed to initiate OAuth flow:', error);
    throw error;
  }
};

// Handle OAuth redirect and exchange code for tokens
export const handleOAuthRedirect = async (): Promise<boolean> => {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const error = urlParams.get('error');
  const isZohoAuth = urlParams.get('auth') === 'zoho';
  
  if (!isZohoAuth) {
    return false;
  }
  
  // Clear auth param from URL
  const newUrl = new URL(window.location.href);
  newUrl.searchParams.delete('auth');
  newUrl.searchParams.delete('code');
  newUrl.searchParams.delete('error');
  window.history.replaceState({}, '', newUrl.toString());
  
  if (error) {
    throw new Error(`Zoho OAuth Error: ${error}`);
  }
  
  if (!code) {
    return false;
  }
  
  try {
    await exchangeCodeForTokens(code);
    return true;
  } catch (error) {
    console.error('Error handling OAuth redirect:', error);
    throw error;
  }
};
