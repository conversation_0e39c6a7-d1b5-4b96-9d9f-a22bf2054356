
import axios from 'axios';
import { FormData } from '../types/formTypes';

// Function to create a new lead/deal in Zoho CRM
export const sendToZohoCRM = async (useOAuth: boolean, formData: FormData) => {
  try {
    const { clientInfo, qualificationInfo, systemCategories } = formData;
    
    // Format the data for Zoho CRM
    const leadData = {
      // Map client info
      client_name: clientInfo.fullName || `${clientInfo.firstName} ${clientInfo.lastName}`.trim(),
      email: clientInfo.email,
      phone: clientInfo.phone,
      address: clientInfo.address || '',
      city: clientInfo.city || '',
      state: clientInfo.state || '',
      zipcode: clientInfo.zipCode || '',
      
      // Map qualification info
      budget: qualificationInfo.budget,
      timeline: qualificationInfo.timeline || '',
      purpose: qualificationInfo.purpose || '',
      project_stage: qualificationInfo.projectStage || '',
      square_footage: qualificationInfo.squareFootage || 0,
      completion_date: qualificationInfo.completionDate ? 
        (typeof qualificationInfo.completionDate === 'string' ? 
          qualificationInfo.completionDate : 
          qualificationInfo.completionDate.toISOString().split('T')[0]) : 
        '',
      
      // Map selected systems
      systems_selected: Object.entries(systemCategories)
        .filter(([_, selected]) => selected)
        .map(([name]) => name)
        .join(', '),
    };

    // If using OAuth, make a direct API call
    if (useOAuth) {
      // Here we would typically call a backend endpoint that handles the OAuth flow
      // For this example, we'll simulate a successful response
      
      // In a real implementation, this would be:
      // const response = await axios.post('/api/zoho/create-lead', leadData);
      
      // Simulated successful response
      return {
        success: true,
        message: "Successfully created deal in Zoho CRM",
        dealUrl: "https://crm.zoho.com/crm/org123456/tab/Leads/123456000000012345",
      };
    } else {
      // Legacy API key method (not recommended but kept for compatibility)
      // This would typically use a stored API key
      // Simulated response
      return {
        success: true,
        message: "Successfully created deal in Zoho CRM via API key",
      };
    }
  } catch (error) {
    console.error("Error creating Zoho lead:", error);
    return {
      success: false,
      message: error && typeof error === 'object' && 'message' in error ? error.message as string : "Unknown error occurred",
    };
  }
};

// Additional helper function to fetch Zoho CRM data
export const fetchZohoData = async () => {
  try {
    // In a real implementation, this would call your backend API that handles Zoho OAuth
    // const response = await axios.get('/api/zoho/get-data');
    // return response.data;
    
    // Simulated response
    return {
      success: true,
      data: [],
    };
  } catch (error) {
    console.error("Error fetching Zoho data:", error);
    return {
      success: false,
      message: error && typeof error === 'object' && 'message' in error ? error.message as string : "Failed to fetch data from Zoho",
    };
  }
};
