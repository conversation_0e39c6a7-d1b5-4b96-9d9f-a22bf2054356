import { v4 as uuidv4 } from 'uuid';

// Types
export type WalkthroughOverride = {
  clientName?: string;
  emailAddress?: string;
  phoneNumber?: string;
  companyName?: string;
  clientType?: string;
  budget?: string;
  timeline?: string;
  existingHome?: boolean;
  existingEquipment?: boolean;
  existingHighEndAV?: boolean;
};

export interface WeQuotePackageItem {
  id: string;
  name: string;
  description?: string;
  systemCategory: string; // this is required
  unitCost: number;
  quantity: number;
  unit: string;
  tradePrice?: number;
  msrp?: number;
  model?: string;
  skuNumber?: string;
  manufacturer?: string;
  manufacturerSku?: string;
  imageUrl?: string;
  documentName?: string;
  documentUrl?: string;
  ean?: string;
  discontinued?: boolean;
  isDefault?: boolean;
  roomType?: string;
  subcategories?: string[];
  categoryGroup?: string;
}

export interface WeQuotePackageGroup {
  id: string;
  name: string;
  description?: string;
  systemCategory: string;
  items: WeQuotePackageItem[];
  label?: string;
  isDefault?: boolean;
  roomType?: string;
}

// Adding WeQuotePackage interface that seems to be used elsewhere
export interface WeQuotePackage {
  id: string;
  name: string;
  description: string;
  systemCategory: string;
  subcategories: string[];
  isDefault: boolean;
  imageUrl?: string;
  msrp?: number;
  tradePrice?: number;
  items?: any[];  // Items included in the package
  sku?: string;
  documentUrl?: string;
  documentName?: string;
  manufacturer?: string;
  manufacturerSku?: string; // Added this property
  categoryGroup?: string; // Added this property
  longDescription?: string; // Added this property
}

export interface ZapierConfig {
  webhook?: string;
  apiKey?: string;
  zapId?: string;
}

// Function to get Zapier webhook URL from localStorage
export const getZapierWebhookUrl = (): string => {
  return localStorage.getItem('zapierWebhookUrl') || '';
};

// Function to save Zapier webhook URL to localStorage
export const saveZapierWebhookUrl = (url: string): void => {
  localStorage.setItem('zapierWebhookUrl', url);
};

// Function to clear Zapier webhook URL from localStorage
export const clearZapierWebhookUrl = (): void => {
  localStorage.removeItem('zapierWebhookUrl');
};

// Function to send data to Zapier webhook
export const sendToZapier = async (webhookUrl: string, payload: any): Promise<any> => {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
      mode: 'no-cors', // Add this to handle CORS
    });
    
    // Since we're using no-cors, we won't get a proper response status
    // So we'll just assume it worked
    return { success: true };
  } catch (error) {
    console.error("Error sending data to Zapier:", error);
    throw error;
  }
};

// Synchronous function to get mock WeQuote packages - no longer async
export const getWeQuotePackages = (): WeQuotePackage[] => {
  try {
    // Return empty array for now
    return [];
  } catch (error) {
    console.error("Error getting WeQuote packages:", error);
    return [];
  }
};

// Synchronous function to get default WeQuote packages - no longer async
export const getDefaultWeQuotePackages = (): WeQuotePackage[] => {
  try {
    // Return empty array for now
    return [];
  } catch (error) {
    console.error("Error getting default WeQuote packages:", error);
    return [];
  }
};

export const getPackagesBySystemCategory = (packages: WeQuotePackage[], category: string): WeQuotePackage[] => {
  return packages.filter(pkg => pkg.systemCategory === category);
};

export const applyDefaultPackages = (formData: any): any => {
  // Placeholder implementation
  return formData;
};

export const createZapierPDF = async (pdfBytes: Uint8Array) => {
  const config = await getZapierConfig();
  
  if (!config.webhook) {
    console.error('Zapier webhook URL not configured');
    return { success: false, error: 'Zapier webhook URL not configured' };
  }

  try {
    const formData = new FormData();
    const pdfBlob = new Blob([pdfBytes], { type: 'application/pdf' });
    formData.append('file', pdfBlob, 'walkthrough.pdf');

    const response = await fetch(config.webhook, {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    console.error('Error sending PDF to Zapier:', error);
    return { success: false, error };
  }
};

export const createWeQuoteProduct = async (
  name: string, 
  description: string, 
  unitCost: number, 
  quantity: number = 1,
  unit: string = 'piece'
): Promise<WeQuotePackageItem | null> => {
  try {
    const id = uuidv4();
    
    // Create a properly structured WeQuotePackageItem with systemCategory
    const newItem: WeQuotePackageItem = {
      id,
      name,
      description,
      unitCost,
      quantity,
      unit,
      systemCategory: 'lighting' // Default system category
    };
    
    return newItem;
  } catch (error) {
    console.error('Error creating WeQuoteProduct:', error);
    return null;
  }
};

// Function to get Zapier configuration from localStorage only
export const getZapierConfig = async (): Promise<ZapierConfig> => {
  try {
    let config: ZapierConfig = {
      webhook: '',
      apiKey: '',
      zapId: ''
    };
    
    // Get from local storage
    const storedConfig = localStorage.getItem('zapierConfig');
    if (storedConfig) {
      try {
        config = JSON.parse(storedConfig);
      } catch (e) {
        console.error('Error parsing zapier config from localStorage:', e);
      }
    }
    
    // Get individual webhook URL if available
    const webhookUrl = getZapierWebhookUrl();
    if (webhookUrl && !config.webhook) {
      config.webhook = webhookUrl;
    }
    
    return config;
  } catch (error) {
    console.error('Error getting Zapier config:', error);
    return {};
  }
};

// Function to save Zapier configuration to localStorage only
export const saveZapierConfig = async (config: ZapierConfig): Promise<boolean> => {
  try {
    // Save to local storage
    localStorage.setItem('zapierConfig', JSON.stringify(config));
    
    // Also update the separate webhook URL if provided
    if (config.webhook) {
      saveZapierWebhookUrl(config.webhook);
    }
    
    return true;
  } catch (error) {
    console.error('Error saving Zapier config:', error);
    return false;
  }
};
