import { Quote, SavedCostStructure, QuoteDocument } from '../models/Quote';
import { CostStructure, CostSummary } from '../models/CostStructure';

// Get all saved cost structures
export const getSavedCostStructures = async (): Promise<SavedCostStructure[]> => {
  try {
    const saved = localStorage.getItem('savedCostStructures');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Error getting saved cost structures:', error);
    return [];
  }
};

// Save a cost structure for later use
export const saveCostStructure = async (
  name: string,
  description: string,
  costStructure: CostStructure,
  costSummary: CostSummary,
  isTemplate: boolean = false
): Promise<string | null> => {
  try {
    const id = `cost-structure-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const savedStructure: SavedCostStructure = {
      id,
      name,
      description,
      costStructure,
      costSummary,
      createdAt: new Date(),
      updatedAt: new Date(),
      isTemplate,
      tags: []
    };

    const saved = await getSavedCostStructures();
    saved.push(savedStructure);
    localStorage.setItem('savedCostStructures', JSON.stringify(saved));
    
    return id;
  } catch (error) {
    console.error('Error saving cost structure:', error);
    return null;
  }
};

// Get a saved cost structure by ID
export const getSavedCostStructureById = async (id: string): Promise<SavedCostStructure | null> => {
  try {
    const saved = await getSavedCostStructures();
    return saved.find(s => s.id === id) || null;
  } catch (error) {
    console.error('Error getting saved cost structure:', error);
    return null;
  }
};

// Delete a saved cost structure
export const deleteSavedCostStructure = async (id: string): Promise<boolean> => {
  try {
    const saved = await getSavedCostStructures();
    const filtered = saved.filter(s => s.id !== id);
    localStorage.setItem('savedCostStructures', JSON.stringify(filtered));
    return true;
  } catch (error) {
    console.error('Error deleting saved cost structure:', error);
    return false;
  }
};

// Get all quotes
export const getQuotes = async (): Promise<Quote[]> => {
  try {
    const quotes = localStorage.getItem('quotes');
    return quotes ? JSON.parse(quotes) : [];
  } catch (error) {
    console.error('Error getting quotes:', error);
    return [];
  }
};

// Save a quote
export const saveQuote = async (
  title: string,
  clientName: string,
  clientEmail: string,
  projectType: string,
  costStructure: CostStructure,
  costSummary: CostSummary,
  notes?: string,
  validUntil?: Date
): Promise<string | null> => {
  try {
    const id = `quote-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const quote: Quote = {
      id,
      title,
      clientName,
      clientEmail,
      projectType,
      costStructure,
      costSummary,
      status: 'draft',
      notes,
      validUntil,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const quotes = await getQuotes();
    quotes.push(quote);
    localStorage.setItem('quotes', JSON.stringify(quotes));
    
    return id;
  } catch (error) {
    console.error('Error saving quote:', error);
    return null;
  }
};

// Get a quote by ID
export const getQuoteById = async (id: string): Promise<Quote | null> => {
  try {
    const quotes = await getQuotes();
    return quotes.find(q => q.id === id) || null;
  } catch (error) {
    console.error('Error getting quote:', error);
    return null;
  }
};

// Update quote status
export const updateQuoteStatus = async (id: string, status: Quote['status']): Promise<boolean> => {
  try {
    const quotes = await getQuotes();
    const index = quotes.findIndex(q => q.id === id);
    
    if (index === -1) return false;
    
    quotes[index].status = status;
    quotes[index].updatedAt = new Date();
    
    localStorage.setItem('quotes', JSON.stringify(quotes));
    return true;
  } catch (error) {
    console.error('Error updating quote status:', error);
    return false;
  }
};

// Delete a quote
export const deleteQuote = async (id: string): Promise<boolean> => {
  try {
    const quotes = await getQuotes();
    const filtered = quotes.filter(q => q.id !== id);
    localStorage.setItem('quotes', JSON.stringify(filtered));
    return true;
  } catch (error) {
    console.error('Error deleting quote:', error);
    return false;
  }
};
