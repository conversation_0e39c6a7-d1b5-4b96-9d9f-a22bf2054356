
// OpenAI API service for generating AI recommendations and analysis
// Note: API key management should be handled securely in production

export interface OpenAIResponse {
  success: boolean;
  content: string;
  error?: string;
}

export interface StreamCallbacks {
  onChunk: (chunk: string) => void;
  onComplete: (fullContent: string) => void;
  onError: (error: string) => void;
}

export async function generateRecommendations(
  systemDescription: string,
  prompt: string,
  apiKey: string
): Promise<OpenAIResponse> {
  if (!apiKey) {
    return {
      success: false,
      content: "",
      error: "API key is required",
    };
  }

  try {
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: systemDescription,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 1000,
      }),
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error?.message || "Failed to generate recommendations");
    }

    return {
      success: true,
      content: data.choices[0]?.message?.content || "",
    };
  } catch (error) {
    console.error("Error generating OpenAI recommendations:", error);
    return {
      success: false,
      content: "",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

export async function generateRecommendationsStream(
  systemDescription: string,
  prompt: string, 
  apiKey: string,
  callbacks: StreamCallbacks
): Promise<void> {
  if (!apiKey) {
    callbacks.onError("API key is required");
    return;
  }

  try {
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: systemDescription,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 1000,
        stream: true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error?.message || "Failed to generate recommendations");
    }

    if (!response.body) {
      throw new Error("Response body is null");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let fullContent = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split("\n").filter(line => line.trim() !== "");
      
      for (const line of lines) {
        if (line.includes("data: [DONE]")) continue;
        
        if (line.startsWith("data: ")) {
          try {
            const jsonData = JSON.parse(line.slice(6));
            const content = jsonData.choices[0]?.delta?.content || "";
            if (content) {
              fullContent += content;
              callbacks.onChunk(content);
            }
          } catch (err) {
            console.warn("Error parsing JSON from stream:", err);
          }
        }
      }
    }

    callbacks.onComplete(fullContent);
  } catch (error) {
    console.error("Error generating OpenAI recommendations stream:", error);
    callbacks.onError(error instanceof Error ? error.message : "Unknown error occurred");
  }
}

export function getOpenAIKey(): string {
  return localStorage.getItem("openai_api_key") || "";
}

export function saveOpenAIKey(apiKey: string): void {
  localStorage.setItem("openai_api_key", apiKey);
}

export function clearOpenAIKey(): void {
  localStorage.removeItem("openai_api_key");
}
