import { CostStructure, CostSummary, CostLineItem, CostCategory } from "../models/CostStructure";
import { RoomConfig } from "../types/roomTypes";
import { SystemCategories } from "../types/formTypes";

/**
 * Calculate the total cost for a single line item
 */
export const calculateLineItemTotal = (item: CostLineItem): number => {
  // Base material cost
  let itemTotal = item.unitCost * item.quantity;
  
  // Add labor if applicable
  if (item.laborHours && item.laborRate) {
    itemTotal += item.laborHours * item.laborRate;
  }
  
  // Apply markup if applicable
  if (item.markup && item.markup > 0) {
    itemTotal += itemTotal * (item.markup / 100);
  }
  
  // Apply discount if applicable
  if (item.discount && item.discount > 0) {
    itemTotal -= itemTotal * (item.discount / 100);
  }
  
  return itemTotal;
};

/**
 * Calculate the total cost for a category
 */
export const calculateCategoryTotal = (category: CostCategory): number => {
  return category.items.reduce((total, item) => total + calculateLineItemTotal(item), 0);
};

/**
 * Generate a cost summary from a cost structure
 */
export const generateCostSummary = (costStructure: CostStructure): CostSummary => {
  let materials = 0;
  let labor = 0;
  
  // Calculate subtotal without markup or tax
  let subtotal = 0;
  const breakdown: { categoryName: string; amount: number }[] = [];
  
  costStructure.categories.forEach(category => {
    let categoryTotal = 0;
    
    category.items.forEach(item => {
      const itemTotal = calculateLineItemTotal(item);
      categoryTotal += itemTotal;
      
      // Track materials vs labor
      if (item.laborHours && item.laborRate) {
        labor += item.laborHours * item.laborRate;
        materials += item.unitCost * item.quantity;
      } else {
        materials += itemTotal;
      }
    });
    
    subtotal += categoryTotal;
    breakdown.push({
      categoryName: category.name,
      amount: categoryTotal
    });
  });
  
  // Apply general markup if applicable
  if (costStructure.generalMarkup > 0) {
    subtotal += subtotal * (costStructure.generalMarkup / 100);
  }
  
  // Calculate tax
  const tax = subtotal * (costStructure.taxRate / 100);
  
  // Calculate discount (assuming discount is tracked at line item level)
  const discount = costStructure.categories.reduce((total, category) => {
    return total + category.items.reduce((catDiscount, item) => {
      if (item.discount && item.discount > 0) {
        return catDiscount + (item.unitCost * item.quantity * (item.discount / 100));
      }
      return catDiscount;
    }, 0);
  }, 0);
  
  // Calculate final total
  const total = subtotal + tax;
  
  return {
    subtotal,
    labor,
    materials,
    tax,
    discount,
    total,
    breakdown
  };
};

/**
 * Generate a cost structure from room data in a walkthrough
 */
export const generateCostStructureFromRooms = (
  rooms: RoomConfig[], 
  systemCategories: SystemCategories,
  clientName: string = "Unnamed Client"
): CostStructure => {
  // Create a new cost structure
  const costStructure: CostStructure = {
    id: `CS-${Math.floor(100000 + Math.random() * 900000)}`,
    name: `${clientName} Project Estimate`,
    description: `Generated from client walkthrough for ${clientName}`,
    taxRate: 8.25, // Default tax rate
    generalMarkup: 10, // Default markup
    createdAt: new Date(),
    updatedAt: new Date(),
    categories: []
  };
  
  // Create categories based on rooms
  const roomCategories: Record<string, CostCategory> = {};
  
  // Process each room
  rooms.forEach(room => {
    // Create a category for this room if it doesn't exist
    if (!roomCategories[room.name]) {
      roomCategories[room.name] = {
        id: `CAT-${Math.floor(100000 + Math.random() * 900000)}`,
        name: room.name,
        description: `${room.name}${room.floor ? ` (Floor: ${room.floor})` : ''}`,
        items: []
      };
    }
    
    // Add line items based on system settings
    Object.entries(room.systemSettings || {}).forEach(([system, isEnabled]) => {
      if (isEnabled) {
        // Only add line items for enabled systems
        const systemName = system.charAt(0).toUpperCase() + system.slice(1);
        
        // Add base system component
        roomCategories[room.name].items.push({
          id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
          name: `${systemName} - Base Component`,
          category: "Hardware",
          description: `Primary ${systemName.toLowerCase()} system for ${room.name}`,
          unitCost: getDefaultCostForSystem(system),
          quantity: 1,
          unit: "piece",
          markup: 15,
          laborHours: getDefaultLaborForSystem(system),
          laborRate: 85
        });
        
        // Add installation materials
        if (needsInstallationMaterials(system)) {
          roomCategories[room.name].items.push({
            id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
            name: `${systemName} - Installation Materials`,
            category: "Materials",
            description: `Cables, mounts and accessories for ${systemName.toLowerCase()}`,
            unitCost: getDefaultCostForSystem(system) * 0.15, // 15% of the main component cost
            quantity: 1,
            unit: "set",
            markup: 20
          });
        }
        
        // Add system-specific items
        const additionalItems = getAdditionalItemsForSystem(system, room);
        if (additionalItems.length > 0) {
          roomCategories[room.name].items.push(...additionalItems);
        }
      }
    });
  });
  
  // Convert room categories to array and add to cost structure
  costStructure.categories = Object.values(roomCategories);
  
  // Add a general category for project-wide costs
  costStructure.categories.push({
    id: `CAT-${Math.floor(100000 + Math.random() * 900000)}`,
    name: "Project Costs",
    description: "General costs applicable to the entire project",
    items: [
      {
        id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
        name: "Project Management",
        category: "Labor",
        description: "Overall project management and coordination",
        unitCost: 0,
        quantity: 1,
        unit: "project",
        laborHours: rooms.length * 2, // 2 hours per room
        laborRate: 95
      },
      {
        id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
        name: "System Programming",
        category: "Labor",
        description: "Programming and configuration of all systems",
        unitCost: 0,
        quantity: 1,
        unit: "project",
        laborHours: rooms.length * 1.5, // 1.5 hours per room
        laborRate: 110
      }
    ]
  });
  
  return costStructure;
};

/**
 * Get default cost estimate for a system type
 */
function getDefaultCostForSystem(system: string): number {
  switch (system.toLowerCase()) {
    case 'lighting': return 299.99;
    case 'shades': return 499.99;
    case 'audio': return 599.99;
    case 'video': return 999.99;
    case 'network': return 399.99;
    case 'security': return 699.99;
    case 'controlsystems': return 799.99;
    default: return 299.99;
  }
}

/**
 * Get default labor hours for installing a system
 */
function getDefaultLaborForSystem(system: string): number {
  switch (system.toLowerCase()) {
    case 'lighting': return 1.5;
    case 'shades': return 2;
    case 'audio': return 3;
    case 'video': return 4;
    case 'network': return 2;
    case 'security': return 2.5;
    case 'controlsystems': return 3;
    default: return 2;
  }
}

/**
 * Determine if a system needs installation materials as a separate line item
 */
function needsInstallationMaterials(system: string): boolean {
  // Most systems need installation materials
  return !['security', 'controlsystems'].includes(system.toLowerCase());
}

/**
 * Get additional line items for specific system types
 */
function getAdditionalItemsForSystem(system: string, room: RoomConfig): CostLineItem[] {
  const items: CostLineItem[] = [];
  const systemLower = system.toLowerCase();
  
  if (systemLower === 'audio') {
    items.push({
      id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
      name: "In-ceiling Speakers",
      category: "Hardware",
      description: "Premium ceiling speakers for room audio",
      unitCost: 149.99,
      quantity: 2,
      unit: "piece",
      markup: 20,
      laborHours: 1,
      laborRate: 85
    });
  }
  
  if (systemLower === 'video') {
    const displayType = room.video || 'display';
    if (displayType === 'projector') {
      items.push({
        id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
        name: "Projector",
        category: "Hardware",
        description: "4K projector",
        unitCost: 1499.99,
        quantity: 1,
        unit: "piece",
        markup: 15,
        laborHours: 2,
        laborRate: 85
      });
      items.push({
        id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
        name: "Projector Screen",
        category: "Hardware",
        description: "Motorized projection screen",
        unitCost: 799.99,
        quantity: 1,
        unit: "piece",
        markup: 15,
        laborHours: 2,
        laborRate: 85
      });
    } else {
      items.push({
        id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
        name: "Display",
        category: "Hardware",
        description: "4K Smart TV",
        unitCost: 1299.99,
        quantity: 1,
        unit: "piece",
        markup: 12,
        laborHours: 2,
        laborRate: 85
      });
    }
  }
  
  if (systemLower === 'lighting' && room.shades === 'motorized') {
    items.push({
      id: `ITEM-${Math.floor(100000 + Math.random() * 900000)}`,
      name: "Motorized Shades",
      category: "Hardware",
      description: "Smart motorized window shades",
      unitCost: 599.99,
      quantity: 2, // Assuming 2 windows on average
      unit: "piece",
      markup: 18,
      laborHours: 1.5,
      laborRate: 85
    });
  }
  
  return items;
}

/**
 * Generate a sample cost structure template
 */
export const generateSampleCostStructure = (): CostStructure => {
  return {
    id: `CS-${Math.floor(100000 + Math.random() * 900000)}`,
    name: "Standard Home Automation Package",
    description: "Basic home automation setup for residential projects",
    taxRate: 8.25,
    generalMarkup: 10,
    createdAt: new Date(),
    updatedAt: new Date(),
    categories: [
      {
        id: "CAT-1",
        name: "Control Systems",
        description: "Primary control systems and hubs",
        items: [
          {
            id: "ITEM-1-1",
            name: "Main Controller Hub",
            category: "Hardware",
            description: "Central control system for home automation",
            unitCost: 599.99,
            quantity: 1,
            unit: "piece",
            markup: 15,
            laborHours: 2,
            laborRate: 85
          },
          {
            id: "ITEM-1-2",
            name: "Secondary Controller",
            category: "Hardware",
            unitCost: 299.99,
            quantity: 2,
            unit: "piece",
            markup: 15,
            laborHours: 1,
            laborRate: 85
          }
        ]
      },
      {
        id: "CAT-2",
        name: "Lighting Control",
        description: "Smart lighting controls and switches",
        items: [
          {
            id: "ITEM-2-1",
            name: "Smart Dimmer Switch",
            category: "Hardware",
            unitCost: 49.99,
            quantity: 8,
            unit: "piece",
            markup: 20,
            laborHours: 0.5,
            laborRate: 85
          },
          {
            id: "ITEM-2-2",
            name: "LED Strip Controller",
            category: "Hardware",
            unitCost: 79.99,
            quantity: 3,
            unit: "piece",
            markup: 15,
            laborHours: 0.75,
            laborRate: 85
          }
        ]
      },
      {
        id: "CAT-3",
        name: "Audio & Video",
        description: "Smart audio and video equipment",
        items: [
          {
            id: "ITEM-3-1",
            name: "Multi-room Audio Amplifier",
            category: "Hardware",
            unitCost: 699.99,
            quantity: 1,
            unit: "piece",
            markup: 12,
            laborHours: 3,
            laborRate: 95
          },
          {
            id: "ITEM-3-2",
            name: "In-ceiling Speaker",
            category: "Hardware",
            unitCost: 149.99,
            quantity: 6,
            unit: "piece",
            markup: 25,
            laborHours: 1,
            laborRate: 85
          }
        ]
      },
      {
        id: "CAT-4",
        name: "Installation Materials",
        description: "Cables, mounts and other installation materials",
        items: [
          {
            id: "ITEM-4-1",
            name: "Network Cable",
            category: "Materials",
            unitCost: 0.75,
            quantity: 500,
            unit: "foot",
            markup: 30
          },
          {
            id: "ITEM-4-2",
            name: "Cable Termination Kit",
            category: "Materials",
            unitCost: 89.99,
            quantity: 1,
            unit: "kit",
            markup: 15
          }
        ]
      }
    ]
  };
};

/**
 * Import cost structure from WeQuote data format
 */
export const importFromWeQuote = (weQuoteData: any): CostStructure | null => {
  try {
    // This would parse the WeQuote data format and convert it to our structure
    // Since we don't have direct access to the format, this is a placeholder
    console.log("Importing data from WeQuote:", weQuoteData);
    
    // For now, return a sample structure
    return generateSampleCostStructure();
  } catch (error) {
    console.error("Error importing from WeQuote:", error);
    return null;
  }
};
