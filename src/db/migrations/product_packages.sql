-- Create product_packages table for custom packages
CREATE TABLE IF NOT EXISTS public.product_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  system_category TEXT NOT NULL,
  subcategories TEXT[] DEFAULT '{}',
  total_cost DECIMAL DEFAULT 0,
  markup_percentage DECIMAL DEFAULT 0,
  final_price DECIMAL DEFAULT 0,
  is_default BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  team_id UUID REFERENCES teams(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create product_package_items table for items within packages
CREATE TABLE IF NOT EXISTS public.product_package_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  package_id UUID NOT NULL REFERENCES product_packages(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES product_catalog_items(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  custom_price DECIMAL NULL, -- Optional custom price override
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  UNIQUE(package_id, product_id) -- Prevent duplicate products in same package
);

-- Add comments to the tables
COMMENT ON TABLE public.product_packages IS 'Stores custom product packages created by combining multiple products';
COMMENT ON TABLE public.product_package_items IS 'Stores the individual products that make up each package';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_product_packages_system_category ON public.product_packages(system_category);
CREATE INDEX IF NOT EXISTS idx_product_packages_team_id ON public.product_packages(team_id);
CREATE INDEX IF NOT EXISTS idx_product_packages_created_by ON public.product_packages(created_by);
CREATE INDEX IF NOT EXISTS idx_product_package_items_package_id ON public.product_package_items(package_id);
CREATE INDEX IF NOT EXISTS idx_product_package_items_product_id ON public.product_package_items(product_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.product_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_package_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for product_packages
CREATE POLICY "Users can view packages from their team" ON public.product_packages
  FOR SELECT USING (
    team_id = (auth.jwt() ->> 'team_id')::UUID OR
    team_id IS NULL -- Allow access to packages without team_id for now
  );

CREATE POLICY "Users can insert packages for their team" ON public.product_packages
  FOR INSERT WITH CHECK (
    team_id = (auth.jwt() ->> 'team_id')::UUID OR
    team_id IS NULL -- Allow creation without team_id for now
  );

CREATE POLICY "Users can update packages from their team" ON public.product_packages
  FOR UPDATE USING (
    team_id = (auth.jwt() ->> 'team_id')::UUID OR
    team_id IS NULL -- Allow updates to packages without team_id for now
  );

CREATE POLICY "Users can delete packages from their team" ON public.product_packages
  FOR DELETE USING (
    team_id = (auth.jwt() ->> 'team_id')::UUID OR
    team_id IS NULL -- Allow deletion of packages without team_id for now
  );

-- Create RLS policies for product_package_items
CREATE POLICY "Users can view package items from their team packages" ON public.product_package_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM product_packages 
      WHERE id = package_id 
      AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
    )
  );

CREATE POLICY "Users can insert package items for their team packages" ON public.product_package_items
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM product_packages 
      WHERE id = package_id 
      AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
    )
  );

CREATE POLICY "Users can update package items from their team packages" ON public.product_package_items
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM product_packages 
      WHERE id = package_id 
      AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
    )
  );

CREATE POLICY "Users can delete package items from their team packages" ON public.product_package_items
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM product_packages 
      WHERE id = package_id 
      AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
    )
  );

-- Create a function to automatically update package total cost when items change
CREATE OR REPLACE FUNCTION update_package_total_cost()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the total cost of the package
  UPDATE product_packages 
  SET 
    total_cost = (
      SELECT COALESCE(SUM(
        CASE 
          WHEN ppi.custom_price IS NOT NULL THEN ppi.custom_price * ppi.quantity
          ELSE pci.unit_cost * ppi.quantity
        END
      ), 0)
      FROM product_package_items ppi
      JOIN product_catalog_items pci ON ppi.product_id = pci.id
      WHERE ppi.package_id = COALESCE(NEW.package_id, OLD.package_id)
    ),
    final_price = (
      SELECT COALESCE(SUM(
        CASE 
          WHEN ppi.custom_price IS NOT NULL THEN ppi.custom_price * ppi.quantity
          ELSE pci.unit_cost * ppi.quantity
        END
      ), 0) * (1 + COALESCE(markup_percentage, 0) / 100)
      FROM product_package_items ppi
      JOIN product_catalog_items pci ON ppi.product_id = pci.id
      WHERE ppi.package_id = COALESCE(NEW.package_id, OLD.package_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.package_id, OLD.package_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update package costs
CREATE TRIGGER trigger_update_package_cost_on_insert
  AFTER INSERT ON product_package_items
  FOR EACH ROW EXECUTE FUNCTION update_package_total_cost();

CREATE TRIGGER trigger_update_package_cost_on_update
  AFTER UPDATE ON product_package_items
  FOR EACH ROW EXECUTE FUNCTION update_package_total_cost();

CREATE TRIGGER trigger_update_package_cost_on_delete
  AFTER DELETE ON product_package_items
  FOR EACH ROW EXECUTE FUNCTION update_package_total_cost();
