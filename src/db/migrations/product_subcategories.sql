-- Create product_subcategories table for custom subcategory management
CREATE TABLE IF NOT EXISTS public.product_subcategories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  system_category TEXT NOT NULL,
  order_index INTEGER DEFAULT 0,
  description TEXT,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add comment to the table
COMMENT ON TABLE public.product_subcategories IS 'Stores custom subcategories for product organization';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_product_subcategories_system_category ON public.product_subcategories(system_category);
CREATE INDEX IF NOT EXISTS idx_product_subcategories_order ON public.product_subcategories(system_category, order_index);

-- Create unique constraint to prevent duplicate subcategory names within the same system category
CREATE UNIQUE INDEX IF NOT EXISTS idx_product_subcategories_unique_name
ON public.product_subcategories(system_category, LOWER(name));

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE public.product_subcategories ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for authenticated users
CREATE POLICY IF NOT EXISTS "Allow all operations for authenticated users" ON public.product_subcategories
FOR ALL USING (auth.role() = 'authenticated');

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER IF NOT EXISTS update_product_subcategories_updated_at
BEFORE UPDATE ON public.product_subcategories
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
