
-- Create product_categories table
CREATE TABLE IF NOT EXISTS public.product_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  system_category TEXT NOT NULL,
  subcategories TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add comment to the table
COMMENT ON TABLE public.product_categories IS 'Stores product categories and their related subcategories';

-- Create index on system_category for better query performance
CREATE INDEX IF NOT EXISTS idx_product_categories_system_category ON public.product_categories(system_category);
