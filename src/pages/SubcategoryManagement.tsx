import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import SubcategoryManagementPage from '@/components/subcategories/SubcategoryManagementPage';

const SubcategoryManagement: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header with back button */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Subcategory Management</h1>
            <p className="text-muted-foreground">
              Manage default and custom subcategories for product organization
            </p>
          </div>
        </div>

        {/* Management interface */}
        <SubcategoryManagementPage isStandalone={true} />
      </div>
    </div>
  );
};

export default SubcategoryManagement;
