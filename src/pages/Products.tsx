
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Package, Grid3X3, Settings, PackagePlus, Upload, Download } from 'lucide-react';
import ProductCatalogManager from '@/components/integrations/ProductCatalogManager';
import { PackageManager } from '@/components/packages/PackageManager';
import usePackageManagement from '@/hooks/usePackageManagement';
import ImportCSVDialog from '@/components/integrations/packages/ImportCSVDialog';
import ErrorBoundary from '@/components/ErrorBoundary';

const ProductsPage: React.FC = () => {
  const {
    setIsAddDialogOpen,
    isImportCSVDialogOpen,
    setIsImportCSVDialogOpen,
    handleImportCSVContent
  } = usePackageManagement();

  return (
    <div className="container mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Products</h1>
        <p className="text-muted-foreground mt-2">
          Manage your product catalog, create custom packages, and configure settings.
        </p>
      </div>

      <Separator className="mb-6" />

      <Tabs defaultValue="catalog" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="catalog" className="flex items-center">
            <Grid3X3 className="mr-2 h-4 w-4" />
            <span>Product Catalog</span>
          </TabsTrigger>
          <TabsTrigger value="packages" className="flex items-center">
            <PackagePlus className="mr-2 h-4 w-4" />
            <span>Custom Packages</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="catalog" className="outline-none">
          <ErrorBoundary>
            <ProductCatalogManager hideHeader={false} />
          </ErrorBoundary>
        </TabsContent>
        <TabsContent value="packages" className="outline-none">
          <PackageManager />
        </TabsContent>
        <TabsContent value="settings" className="outline-none">
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold mb-2">Product Settings</h2>
              <p className="text-muted-foreground">
                Manage product catalog settings and import/export options.
              </p>
            </div>

            <div className="grid gap-6">
              <div className="border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-3">Product Management</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Add new products to your catalog or import products from CSV files.
                </p>
                <div className="flex gap-3">
                  <Button variant="default" onClick={() => setIsAddDialogOpen(true)}>
                    <Package className="mr-2 h-4 w-4" />
                    Add Product
                  </Button>
                </div>
              </div>

              <div className="border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-3">Import & Export</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Import products from CSV files or export your current catalog.
                </p>
                <div className="flex gap-3">
                  <Button variant="outline" onClick={() => setIsImportCSVDialogOpen(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    Import CSV
                  </Button>
                  <Button variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Export Catalog
                  </Button>
                </div>
              </div>

              <div className="border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-3">Categories</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Manage product categories and system classifications.
                </p>
                <div className="flex gap-3">
                  <Button variant="outline">
                    <Settings className="mr-2 h-4 w-4" />
                    Manage Categories
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Import CSV Dialog */}
      <ImportCSVDialog
        open={isImportCSVDialogOpen}
        onOpenChange={setIsImportCSVDialogOpen}
        onImport={(csvContent, fieldMapping) => handleImportCSVContent(csvContent, fieldMapping)}
      />
    </div>
  );
};

export default ProductsPage;
