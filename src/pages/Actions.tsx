import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { sendToZapier, getZapierWebhookUrl, saveZapierWebhookUrl } from '@/services/zapierService';
import { AlertCircle, Send, ExternalLink, RefreshCw, ArrowUpFromLine } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { getWalkthroughs, Walkthrough } from '@/services/walkthroughService';
const Actions = () => {
  const {
    toast
  } = useToast();
  const [webhookUrl, setWebhookUrl] = useState('');
  const [selectedWalkthrough, setSelectedWalkthrough] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingWalkthroughs, setLoadingWalkthroughs] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [walkthroughs, setWalkthroughs] = useState<Walkthrough[]>([]);
  const [syncHistory, setSyncHistory] = useState<any[]>([]);

  // Customer fields based on the image
  const [customerName, setCustomerName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [addressLine1, setAddressLine1] = useState('');
  const [addressLine2, setAddressLine2] = useState('');
  const [addressLine3, setAddressLine3] = useState('');
  const [town, setTown] = useState('');
  const [county, setCounty] = useState('');
  const [postcode, setPostcode] = useState('');
  const [country, setCountry] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [emailAddress, setEmailAddress] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [notes, setNotes] = useState('');

  // Load saved webhook URL on component mount
  useEffect(() => {
    const savedUrl = getZapierWebhookUrl();
    if (savedUrl) {
      setWebhookUrl(savedUrl);
    }

    // Load sync history from localStorage on component mount
    const savedHistory = localStorage.getItem('zapier_sync_history');
    if (savedHistory) {
      setSyncHistory(JSON.parse(savedHistory));
    }
    loadWalkthroughs();
  }, []);
  const loadWalkthroughs = async () => {
    try {
      setLoadingWalkthroughs(true);
      const data = await getWalkthroughs();
      setWalkthroughs(data);
    } catch (err) {
      console.error("Error loading walkthroughs:", err);
      toast({
        variant: "destructive",
        title: "Failed to load walkthroughs",
        description: "Could not retrieve walkthrough data"
      });
    } finally {
      setLoadingWalkthroughs(false);
    }
  };
  const handleWalkthroughChange = (id: string) => {
    setSelectedWalkthrough(id);
    const walkthrough = walkthroughs.find(w => w.id === id);
    if (walkthrough) {
      // Pre-fill fields based on walkthrough data
      setCustomerName(walkthrough.client_name || '');
      if (walkthrough.form_data) {
        const clientInfo = walkthrough.form_data.clientInfo;
        setEmailAddress(clientInfo?.email || '');
        setPhoneNumber(clientInfo?.phone || '');
        setAddressLine1(clientInfo?.address || '');
        setTown(clientInfo?.city || '');
        setCounty(clientInfo?.state || '');
        setPostcode(clientInfo?.zipCode || '');
      }
    }
  };
  const handleSendToZapier = async () => {
    if (!webhookUrl) {
      setError("Webhook URL is required");
      return;
    }
    if (!selectedWalkthrough) {
      setError("Please select a walkthrough");
      return;
    }
    if (!customerName) {
      setError("Customer name is required");
      return;
    }
    try {
      setIsLoading(true);
      setError(null);

      // Save webhook URL for future use
      saveZapierWebhookUrl(webhookUrl);

      // Find the selected walkthrough
      const walkthrough = walkthroughs.find(w => w.id === selectedWalkthrough);
      if (!walkthrough) {
        throw new Error("Selected walkthrough not found");
      }

      // Prepare data to send to Zapier with the new fields
      const payload = {
        customer_name: customerName,
        account_number: accountNumber,
        address: {
          line1: addressLine1,
          line2: addressLine2,
          line3: addressLine3,
          town: town,
          county: county,
          postcode: postcode,
          country: country
        },
        phone_number: phoneNumber,
        email_address: emailAddress,
        website_url: websiteUrl,
        notes: notes,
        client_data: {
          client_info: walkthrough.form_data?.clientInfo || {},
          project_details: {
            budget: walkthrough.form_data?.qualificationInfo?.budget || 0,
            square_footage: walkthrough.form_data?.qualificationInfo?.squareFootage || 0,
            completion_date: walkthrough.form_data?.qualificationInfo?.completionDate || null,
            project_type: walkthrough.form_data?.clientInfo?.projectType || '',
            home_type: walkthrough.form_data?.clientInfo?.homeType || ''
          },
          rooms: walkthrough.form_data?.rooms || [],
          systems: walkthrough.form_data?.systemCategories || {},
          scope_of_work: walkthrough.form_data?.qualificationInfo?.generatedScopeOfWork || walkthrough.form_data?.executiveSummary || ''
        },
        source: "actions_page",
        triggered_at: new Date().toISOString()
      };
      console.log("Sending customer data to Zapier:", payload);
      await sendToZapier(webhookUrl, payload);

      // Create new history item
      const newHistoryItem = {
        id: Date.now().toString(),
        walkthroughId: selectedWalkthrough,
        customerName: customerName,
        timestamp: new Date().toISOString(),
        walkthrough: walkthrough.client_name // Add walkthrough name for display
      };

      // Update sync history (add to beginning of array)
      setSyncHistory(prevHistory => [newHistoryItem, ...prevHistory]);

      // Store sync history in localStorage to persist it
      localStorage.setItem('zapier_sync_history', JSON.stringify([newHistoryItem, ...syncHistory]));
      toast({
        title: "Data sent successfully",
        description: `Customer data for "${customerName}" has been sent to Zapier.`
      });

      // Reset form fields but keep webhook URL
      setNotes('');
    } catch (err) {
      console.error("Error sending customer data to Zapier:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
      toast({
        variant: "destructive",
        title: "Failed to send data",
        description: "There was an error sending the customer data to Zapier. Please try again."
      });
    } finally {
      setIsLoading(false);
    }
  };
  return <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Walkthrough Data Sync</h1>
      
      <Tabs defaultValue="sync" className="w-full">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="sync" className="flex-1">Sync Walkthrough Data</TabsTrigger>
          <TabsTrigger value="history" className="flex-1">Sync History</TabsTrigger>
        </TabsList>
        
        <TabsContent value="sync">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sync Customer Data to External Systems</CardTitle>
                <CardDescription>
                  Send walkthrough and customer data to external systems via Zapier integrations.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {error && <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>}
                
                <div className="space-y-2">
                  <Label htmlFor="webhook-url">Zapier Webhook URL</Label>
                  <Input id="webhook-url" placeholder="https://hooks.zapier.com/hooks/catch/..." value={webhookUrl} onChange={e => setWebhookUrl(e.target.value)} />
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="walkthrough">Select Walkthrough</Label>
                    <Button variant="ghost" size="sm" onClick={loadWalkthroughs} disabled={loadingWalkthroughs} className="h-8 px-2 text-xs">
                      <RefreshCw className={`h-3 w-3 mr-1 ${loadingWalkthroughs ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                  <Select value={selectedWalkthrough} onValueChange={handleWalkthroughChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a walkthrough" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingWalkthroughs ? <SelectItem value="loading" disabled>Loading walkthroughs...</SelectItem> : walkthroughs.length > 0 ? walkthroughs.map(walkthrough => <SelectItem key={walkthrough.id} value={walkthrough.id}>
                            {walkthrough.client_name || 'Unnamed Client'} 
                            {walkthrough.updated_at && ` (${new Date(walkthrough.updated_at).toLocaleDateString()})`}
                          </SelectItem>) : <SelectItem value="empty" disabled>No walkthroughs found</SelectItem>}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="customer-name">Customer Name *</Label>
                  <Input id="customer-name" placeholder="Enter customer name" value={customerName} onChange={e => setCustomerName(e.target.value)} required />
                </div>
                
                
                
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Address Information</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address-line-1">Address Line 1</Label>
                    <Input id="address-line-1" placeholder="Enter address line 1" value={addressLine1} onChange={e => setAddressLine1(e.target.value)} />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address-line-2">Address Line 2</Label>
                    <Input id="address-line-2" placeholder="Enter address line 2" value={addressLine2} onChange={e => setAddressLine2(e.target.value)} />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address-line-3">Address Line 3</Label>
                    <Input id="address-line-3" placeholder="Enter address line 3" value={addressLine3} onChange={e => setAddressLine3(e.target.value)} />
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="town">Town</Label>
                      <Input id="town" placeholder="Enter town" value={town} onChange={e => setTown(e.target.value)} />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="county">State</Label>
                      <Input id="county" placeholder="Enter county" value={county} onChange={e => setCounty(e.target.value)} />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="postcode">Zip Code</Label>
                      <Input id="postcode" placeholder="Enter postcode" value={postcode} onChange={e => setPostcode(e.target.value)} />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input id="country" placeholder="Enter country" value={country} onChange={e => setCountry(e.target.value)} />
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone-number">Phone Number</Label>
                    <Input id="phone-number" placeholder="Enter phone number" value={phoneNumber} onChange={e => setPhoneNumber(e.target.value)} />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email-address">Email Address</Label>
                    <Input id="email-address" type="email" placeholder="Enter email address" value={emailAddress} onChange={e => setEmailAddress(e.target.value)} />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="website-url">Website URL</Label>
                  <Input id="website-url" placeholder="Enter website URL" value={websiteUrl} onChange={e => setWebsiteUrl(e.target.value)} />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea id="notes" placeholder="Enter additional notes" value={notes} onChange={e => setNotes(e.target.value)} rows={3} />
                </div>
              </CardContent>
              
              <CardFooter>
                <Button onClick={handleSendToZapier} disabled={isLoading || !webhookUrl || !selectedWalkthrough || !customerName} className="w-full">
                  {isLoading ? <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Sending Data...
                    </span> : <span className="flex items-center">
                      <ArrowUpFromLine className="mr-2 h-4 w-4" />
                      Sync Customer Data
                    </span>}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Zapier Integration Guide</CardTitle>
                <CardDescription>
                  How to set up a Zapier integration with your customer data
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <Alert className="bg-blue-50 border-blue-200">
                  <AlertDescription className="text-blue-800">
                    Learn how to connect your customer data to your favorite tools using Zapier.
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-4 py-2">
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                      1
                    </div>
                    <div>
                      <h3 className="font-medium">Create a Zap in Zapier</h3>
                      <p className="text-sm text-muted-foreground">
                        Log into your Zapier account and create a new Zap.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                      2
                    </div>
                    <div>
                      <h3 className="font-medium">Set Up a Webhook Trigger</h3>
                      <p className="text-sm text-muted-foreground">
                        Select "Webhooks by Zapier" as your trigger app and choose "Catch Hook" as the event.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                      3
                    </div>
                    <div>
                      <h3 className="font-medium">Copy the Webhook URL</h3>
                      <p className="text-sm text-muted-foreground">
                        Zapier will generate a unique webhook URL. Copy this URL and paste it in the field on the left.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                      4
                    </div>
                    <div>
                      <h3 className="font-medium">Connect to Your Apps</h3>
                      <p className="text-sm text-muted-foreground">
                        Add action steps to your Zap to send data to CRM systems, spreadsheets, or other tools.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 text-primary rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0 mt-0.5">
                      5
                    </div>
                    <div>
                      <h3 className="font-medium">Map the Fields</h3>
                      <p className="text-sm text-muted-foreground">
                        When setting up your action steps, map the customer data fields using the paths below.
                      </p>
                      <div className="bg-muted p-3 rounded text-xs mt-2 space-y-1">
                        <p><strong>Customer Name:</strong> <code>customer_name</code></p>
                        <p><strong>Account Number:</strong> <code>account_number</code></p>
                        <p><strong>Address Line 1:</strong> <code>address.line1</code></p>
                        <p><strong>Town:</strong> <code>address.town</code></p>
                        <p><strong>Email Address:</strong> <code>email_address</code></p>
                        <p><strong>Walkthrough Data:</strong> <code>client_data</code></p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-center pt-2">
                  <Button variant="outline" className="flex items-center gap-2" onClick={() => window.open('https://zapier.com/apps/webhooks/integrations', '_blank')}>
                    Visit Zapier Webhook Documentation
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Sync History</CardTitle>
              <CardDescription>
                View the history of customer data syncs to external systems
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {syncHistory.length > 0 ? <div className="space-y-4">
                  {syncHistory.map(item => <div key={item.id} className="flex flex-col p-4 border rounded-md">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{item.customerName}</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(item.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground mt-2">Walkthrough ID: {item.walkthroughId}</div>
                    </div>)}
                </div> : <div className="flex flex-col items-center justify-center h-64 p-4 text-center border border-dashed rounded-md text-muted-foreground">
                  <Send className="h-12 w-12 mb-4 text-muted-foreground/50" />
                  <p className="mb-2">No sync history found</p>
                  <p className="text-sm">Customer data syncs will appear here after you send data to Zapier.</p>
                </div>}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>;
};
export default Actions;