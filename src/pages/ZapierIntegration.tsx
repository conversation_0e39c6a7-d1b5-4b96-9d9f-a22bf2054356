
import React, { useState } from 'react';
import ZapierIntegration from '@/components/integrations/ZapierIntegration';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Webhook, FileCog, ArrowRight, Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { getZapierWebhookUrl, saveZapierWebhookUrl } from '@/services/zapierService';

const ZapierIntegrationPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // Load saved webhook URL from localStorage
  const [globalWebhookUrl, setGlobalWebhookUrl] = useState(getZapierWebhookUrl());
  
  const handleSaveGlobalWebhook = () => {
    saveZapierWebhookUrl(globalWebhookUrl);
    toast({
      title: "Webhook URL Saved",
      description: "Your Zapier webhook URL has been saved for all integrations."
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          className="mr-2" 
          onClick={() => navigate('/settings')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Settings
        </Button>
        <h1 className="text-2xl font-bold">Zapier Integration</h1>
      </div>
      
      {/* Global Webhook URL Setting */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            Global Zapier Webhook URL
          </CardTitle>
          <CardDescription>
            Set your Zapier webhook URL once to use across all integrations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 items-end">
            <div className="flex-1 space-y-2">
              <Label htmlFor="global-webhook">Global Webhook URL</Label>
              <Input 
                id="global-webhook"
                placeholder="https://hooks.zapier.com/hooks/catch/..."
                value={globalWebhookUrl}
                onChange={(e) => setGlobalWebhookUrl(e.target.value)}
              />
            </div>
            <Button 
              onClick={handleSaveGlobalWebhook} 
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Save
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            This webhook URL will be used as the default for all Zapier integrations across the application.
          </p>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="setup">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="setup">Setup</TabsTrigger>
          <TabsTrigger value="wequote">WeQuote Integration</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>
        
        <TabsContent value="setup" className="space-y-6 mt-4">
          <ZapierIntegration 
            defaultWebhookUrl={globalWebhookUrl} 
            onComplete={() => {
              // Refresh the global webhook URL state
              setGlobalWebhookUrl(getZapierWebhookUrl());
            }}
          />
          
          <Card>
            <CardHeader>
              <CardTitle>How to Connect</CardTitle>
              <CardDescription>Follow these steps to set up your Zapier integration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Step 1: Create a Zap in Zapier</h3>
                <p className="text-sm text-muted-foreground">
                  Go to your Zapier account and create a new Zap. Choose "Webhook by Zapier" as the trigger app.
                </p>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Step 2: Set Up Webhook Trigger</h3>
                <p className="text-sm text-muted-foreground">
                  Select "Catch Hook" as the trigger event to receive data from this application.
                </p>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Step 3: Copy the Webhook URL</h3>
                <p className="text-sm text-muted-foreground">
                  Zapier will provide you with a custom webhook URL. Copy this URL and paste it in the field above.
                </p>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Step 4: Test the Connection</h3>
                <p className="text-sm text-muted-foreground">
                  Click the "Trigger Zap" button to send a test payload and verify the connection.
                </p>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Step 5: Complete Your Zap</h3>
                <p className="text-sm text-muted-foreground">
                  Configure the actions you want to happen when the Zap is triggered, such as creating records in other apps.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="wequote" className="space-y-6 mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileCog className="h-5 w-5" />
                WeQuote Integration Guide
              </CardTitle>
              <CardDescription>
                Learn how to connect your walkthrough data to WeQuote using Zapier
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-amber-50 border border-amber-200 p-4 rounded-md">
                <p className="text-amber-800 text-sm">
                  This guide will help you set up a Zap that takes your client walkthrough data and sends it to WeQuote to create a quote automatically.
                </p>
              </div>
              
              <div className="space-y-4">
                <div className="flex gap-3">
                  <div className="bg-blue-100 text-blue-600 font-bold rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                    1
                  </div>
                  <div>
                    <h3 className="font-medium">Create a Webhook Trigger in Zapier</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Start a new Zap and select "Webhooks by Zapier" as your trigger app. Choose "Catch Hook" as the event.
                    </p>
                    <div className="bg-muted p-3 rounded text-xs">
                      <code>Trigger App: Webhooks by Zapier {'>'} Catch Hook</code>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <div className="bg-blue-100 text-blue-600 font-bold rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                    2
                  </div>
                  <div>
                    <h3 className="font-medium">Copy Your Webhook URL</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Zapier will generate a unique webhook URL. Copy this URL and paste it into the "Zapier Webhook URL" field in the setup tab.
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="bg-blue-100 text-blue-600 font-bold rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                    3
                  </div>
                  <div>
                    <h3 className="font-medium">Test the Webhook</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Click "Send to WeQuote via Zapier" in our app to send a test payload. In Zapier, click "Test Trigger" to confirm the data is received.
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="bg-blue-100 text-blue-600 font-bold rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                    4
                  </div>
                  <div>
                    <h3 className="font-medium">Add WeQuote as an Action</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Add WeQuote as your action app. If WeQuote has a direct Zapier integration, select it. Otherwise, you can use the HTTP or the WeQuote API action.
                    </p>
                    <div className="bg-muted p-3 rounded text-xs">
                      <code>Action App: WeQuote {'>'} Create Quote</code>
                      <br />
                      <code>Alternative: Webhooks by Zapier {'>'} POST Request to WeQuote API</code>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="bg-blue-100 text-blue-600 font-bold rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                    5
                  </div>
                  <div>
                    <h3 className="font-medium">Map the Fields</h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      Map the fields from the webhook to the corresponding fields in WeQuote. Our walkthrough data includes client information, rooms, systems, and scope of work.
                    </p>
                    <div className="bg-muted p-3 rounded text-xs space-y-1">
                      <p><strong>Client Name:</strong> <code>client_data.client_info.fullName</code></p>
                      <p><strong>Email:</strong> <code>client_data.client_info.email</code></p>
                      <p><strong>Project Type:</strong> <code>client_data.project_details.project_type</code></p>
                      <p><strong>Budget:</strong> <code>client_data.project_details.budget</code></p>
                      <p><strong>Scope of Work:</strong> <code>client_data.scope_of_work</code></p>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="bg-blue-100 text-blue-600 font-bold rounded-full h-6 w-6 flex items-center justify-center flex-shrink-0">
                    6
                  </div>
                  <div>
                    <h3 className="font-medium">Test and Enable Your Zap</h3>
                    <p className="text-sm text-muted-foreground">
                      Test your Zap to ensure it correctly creates a quote in WeQuote with your walkthrough data. Once confirmed, enable the Zap.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-center">
                <Button 
                  variant="outline" 
                  className="flex items-center gap-2"
                  onClick={() => window.open('https://zapier.com/apps/wequote/integrations', '_blank')}
                >
                  Visit WeQuote Integrations on Zapier
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="templates" className="space-y-6 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Zapier Templates</CardTitle>
              <CardDescription>Pre-built Zaps for common workflows</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="rounded-md border p-4 hover:bg-gray-50 transition cursor-pointer">
                <div className="flex items-center gap-4">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Webhook className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Walkthrough to WeQuote</h3>
                    <p className="text-sm text-muted-foreground">
                      Send walkthrough data to WeQuote to generate a quote automatically
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="rounded-md border p-4 hover:bg-gray-50 transition cursor-pointer">
                <div className="flex items-center gap-4">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Webhook className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Walkthrough to Trello</h3>
                    <p className="text-sm text-muted-foreground">
                      Create Trello cards from your walkthrough steps
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="rounded-md border p-4 hover:bg-gray-50 transition cursor-pointer">
                <div className="flex items-center gap-4">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Webhook className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">Share via Email</h3>
                    <p className="text-sm text-muted-foreground">
                      Email walkthrough summaries to stakeholders
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ZapierIntegrationPage;
