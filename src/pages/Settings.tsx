import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { getOpenAIKey, saveOpenAIKey, clearOpenAIKey } from "@/services/openaiService";
import { getZapierWebhookUrl, saveZapierWebhookUrl, clearZapierWebhookUrl } from "@/services/zapierService";
import { useTheme } from "@/components/theme/ThemeProvider";
import { useToast } from "@/hooks/use-toast";
import { Eye, EyeOff, Save, Trash, Database, Zap, Webhook, Settings as SettingsIcon, Package, Filter } from "lucide-react";
import { handleOAuthRedirect } from "@/services/zohoOAuthService";
import { Link, useNavigate } from "react-router-dom";
import ZohoOAuthConfig from "@/components/integrations/ZohoOAuthConfig";

const Settings = () => {
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const navigate = useNavigate();

  const [apiKey, setApiKey] = useState("");
  const [webhookUrl, setWebhookUrl] = useState("");
  const [showApiKey, setShowApiKey] = useState(false);
  const [oauthSuccess, setOauthSuccess] = useState(false);

  useEffect(() => {
    const savedKey = getOpenAIKey();
    setApiKey(savedKey);

    const savedWebhook = getZapierWebhookUrl();
    setWebhookUrl(savedWebhook);

    // Handle OAuth redirect if needed
    const checkOAuthRedirect = async () => {
      try {
        const success = await handleOAuthRedirect();
        if (success) {
          setOauthSuccess(true);
          toast({
            title: "Connected to Zoho CRM",
            description: "OAuth authentication successful. You can now use Zoho CRM integration.",
          });
        }
      } catch (error) {
        console.error("OAuth error:", error);
        toast({
          title: "OAuth Error",
          description: error instanceof Error ? error.message : "Failed to authenticate with Zoho CRM",
          variant: "destructive",
        });
      }
    };

    checkOAuthRedirect();
  }, [toast]);

  const handleSaveApiKey = () => {
    saveOpenAIKey(apiKey);
    toast({
      title: "API Key Saved",
      description: "Your OpenAI API key has been saved",
    });
  };

  const handleClearApiKey = () => {
    setApiKey("");
    clearOpenAIKey();
    toast({
      title: "API Key Removed",
      description: "Your OpenAI API key has been removed",
    });
  };

  const handleSaveWebhookUrl = () => {
    saveZapierWebhookUrl(webhookUrl);
    toast({
      title: "Webhook URL Saved",
      description: "Your Zapier webhook URL has been saved for all integrations",
    });
  };

  const handleClearWebhookUrl = () => {
    setWebhookUrl("");
    clearZapierWebhookUrl();
    toast({
      title: "Webhook URL Removed",
      description: "Your Zapier webhook URL has been removed",
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Settings</h1>

      <Tabs defaultValue="account" className="max-w-3xl">
        <TabsList className="grid grid-cols-4">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your account settings and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input id="name" placeholder="Your name" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="Your email address" />
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="notifications" />
                <Label htmlFor="notifications">Enable email notifications</Label>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button>Save Changes</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Appearance Settings</CardTitle>
              <CardDescription>
                Customize the appearance of the application.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <Label>Theme Mode</Label>
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant={theme === "light" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("light")}
                  >
                    Light
                  </Button>
                  <Button
                    variant={theme === "dark" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("dark")}
                  >
                    Dark
                  </Button>
                  <Button
                    variant={theme === "system" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("system")}
                  >
                    System
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <Label>Theme Variations</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <Button
                    variant={theme === "neon" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("neon")}
                  >
                    Neon
                  </Button>
                  <Button
                    variant={theme === "midnight" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("midnight")}
                  >
                    Midnight
                  </Button>
                  <Button
                    variant={theme === "oceanic" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("oceanic")}
                  >
                    Oceanic
                  </Button>
                  <Button
                    variant={theme === "cyborg" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("cyborg")}
                  >
                    Cyborg
                  </Button>
                  <Button
                    variant={theme === "aurora" ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => setTheme("aurora")}
                  >
                    Aurora
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="animations" defaultChecked />
                <Label htmlFor="animations">Enable animations</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data">
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
              <CardDescription>
                Manage your application data, subcategories, and product organization.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Subcategory Management */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                    <Filter className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Subcategory Management</h3>
                    <p className="text-sm text-muted-foreground">
                      Manage default and custom subcategories for product organization
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/subcategories')}
                    className="flex items-center gap-2"
                  >
                    <SettingsIcon className="h-4 w-4" />
                    Manage
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Control which subcategories appear in dropdowns and filters. Create custom subcategories for your specific needs.
                </div>
              </div>

              {/* Product Data */}
              <div className="space-y-4 border-t pt-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                    <Package className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Product Catalog</h3>
                    <p className="text-sm text-muted-foreground">
                      Manage your product catalog and import/export data
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/products')}
                    className="flex items-center gap-2"
                  >
                    <Package className="h-4 w-4" />
                    Manage
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Add, edit, and organize your product catalog. Import products from CSV files.
                </div>
              </div>

              {/* Data Export/Import */}
              <div className="space-y-4 border-t pt-6">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                    <Database className="h-5 w-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">Data Export & Import</h3>
                    <p className="text-sm text-muted-foreground">
                      Export your data or import from external sources
                    </p>
                  </div>
                  <Button variant="outline" disabled>
                    Coming Soon
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground pl-13">
                  Export walkthroughs, reports, and product data. Import from various file formats.
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations">
          <Card>
            <CardHeader>
              <CardTitle>API Integrations</CardTitle>
              <CardDescription>
                Connect your application with third-party services.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* OpenAI Integration */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="openai-key">OpenAI API Key</Label>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowApiKey(!showApiKey)}
                      className="h-8 w-8"
                    >
                      {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Input
                    id="openai-key"
                    type={showApiKey ? "text" : "password"}
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your OpenAI API key"
                    className="flex-1"
                  />
                </div>
                <div className="flex justify-between mt-2">
                  <Button variant="outline" size="sm" onClick={handleClearApiKey} className="flex items-center gap-1">
                    <Trash className="h-3.5 w-3.5" />
                    Clear
                  </Button>
                  <Button size="sm" onClick={handleSaveApiKey} className="flex items-center gap-1">
                    <Save className="h-3.5 w-3.5" />
                    Save API Key
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Your API key can be found in your OpenAI account dashboard
                </p>
              </div>

              {/* Zapier Integration - Updated Section */}
              <div className="space-y-2 border-t pt-6">
                <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  Zapier Integration
                </h3>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="zapier-webhook">Global Webhook URL</Label>
                  </div>
                  <div className="flex gap-2">
                    <Input
                      id="zapier-webhook"
                      type="text"
                      value={webhookUrl}
                      onChange={(e) => setWebhookUrl(e.target.value)}
                      placeholder="https://hooks.zapier.com/hooks/catch/..."
                      className="flex-1"
                    />
                  </div>
                  <div className="flex justify-between mt-2">
                    <Button variant="outline" size="sm" onClick={handleClearWebhookUrl} className="flex items-center gap-1">
                      <Trash className="h-3.5 w-3.5" />
                      Clear
                    </Button>
                    <Button size="sm" onClick={handleSaveWebhookUrl} className="flex items-center gap-1">
                      <Save className="h-3.5 w-3.5" />
                      Save Webhook URL
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    This webhook URL will be used by default for all Zapier integrations
                  </p>
                </div>

                <div className="mt-4">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => navigate('/zapier')}
                  >
                    <Webhook className="h-4 w-4" />
                    Configure Advanced Zapier Settings
                  </Button>
                </div>
              </div>

              {/* Zoho CRM OAuth Integration */}
              <div className="space-y-2 border-t pt-6">
                <h3 className="text-lg font-medium flex items-center gap-2 mb-4">
                  <Database className="h-5 w-5 text-rose-600" />
                  Zoho CRM Integration
                </h3>

                {oauthSuccess && (
                  <div className="rounded-md bg-green-50 p-4 mb-4 border border-green-200">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-green-800">Successfully Connected</h3>
                        <div className="mt-2 text-sm text-green-700">
                          <p>Your application is now connected to Zoho CRM via OAuth.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <ZohoOAuthConfig />
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Connected Services</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Switch id="weQuote" defaultChecked />
                      <Label htmlFor="weQuote">WeQuote Integration</Label>
                    </div>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
