
import React from 'react';
import CostStructureManager from '@/components/cost/CostStructureManager';
import { useIsMobile } from '@/hooks/use-mobile';
import { useLocation } from 'react-router-dom';

const CostManager: React.FC = () => {
  const isMobile = useIsMobile();
  const location = useLocation();

  return (
    <div className="container mx-auto py-4 md:py-8 px-3 md:px-4">
      <div className="animate-fade-in">
        <h1 className="text-xl md:text-2xl font-bold mb-2 md:mb-6">Cost Manager</h1>
        <p className="mb-4 md:mb-8 text-sm md:text-base text-muted-foreground">
          Create and manage cost structures for your quotes and projects.
        </p>

        <div className={`${isMobile ? 'px-0' : 'px-4'} pb-6`}>
          <CostStructureManager
            walkthroughData={location.state?.walkthroughData}
          />
        </div>
      </div>
    </div>
  );
};

export default CostManager;
