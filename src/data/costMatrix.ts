
// Cost estimation matrix based on https://docs.google.com/spreadsheets/d/1ai6MzdnZaRPOLjn_yvcSwCQ6-qAPfpusSNnJTha3E2g

import { SystemCategories } from '@/types/formTypes';

export interface CostMatrixTier {
  name: string;
  label: string;
  description: string;
  multiplier: number;
}

export interface CostMatrixCategory {
  id: string;
  name: string;
  description: string;
  basePrice: number;
  percentOfTotal: number;
  qualityTiers: {
    basic: number;
    standard: number;
    premium: number;
  };
}

// Quality tier definitions
export const qualityTiers: Record<string, CostMatrixTier> = {
  basic: {
    name: "basic",
    label: "Good",
    description: "Entry level home automation with basic functionality",
    multiplier: 0.6,
  },
  standard: {
    name: "standard",
    label: "Better",
    description: "Mid-range home automation with advanced features", 
    multiplier: 1.0,
  },
  premium: {
    name: "premium",
    label: "Best",
    description: "Premium home automation with high-end components and full integration",
    multiplier: 1.5,
  }
};

// System category definitions with pricing data
export const costMatrixCategories: CostMatrixCategory[] = [
  {
    id: "lighting",
    name: "Lighting Control",
    description: "Smart lighting systems and controls",
    basePrice: 250,
    percentOfTotal: 15,
    qualityTiers: {
      basic: 150,
      standard: 250,
      premium: 400,
    }
  },
  {
    id: "shades",
    name: "Window Treatments",
    description: "Motorized shades and window coverings",
    basePrice: 800,
    percentOfTotal: 10,
    qualityTiers: {
      basic: 500,
      standard: 800,
      premium: 1200,
    }
  },
  {
    id: "audio",
    name: "Audio Systems",
    description: "Whole-home and room-specific audio solutions",
    basePrice: 600,
    percentOfTotal: 20,
    qualityTiers: {
      basic: 400,
      standard: 600,
      premium: 1000,
    }
  },
  {
    id: "video",
    name: "Video Systems",
    description: "TV, projector and multi-room video distribution",
    basePrice: 1200,
    percentOfTotal: 25,
    qualityTiers: {
      basic: 800,
      standard: 1200,
      premium: 2000,
    }
  },
  {
    id: "network",
    name: "Networking",
    description: "Robust home network infrastructure",
    basePrice: 400,
    percentOfTotal: 10,
    qualityTiers: {
      basic: 250,
      standard: 400,
      premium: 650,
    }
  },
  {
    id: "security",
    name: "Security & Surveillance",
    description: "Home security and monitoring systems",
    basePrice: 500,
    percentOfTotal: 10,
    qualityTiers: {
      basic: 300,
      standard: 500,
      premium: 800,
    }
  },
  {
    id: "controlSystems",
    name: "Control Systems",
    description: "Central control system and interfaces",
    basePrice: 750,
    percentOfTotal: 10,
    qualityTiers: {
      basic: 500,
      standard: 750,
      premium: 1200,
    }
  }
];

// Square footage price multipliers
export const sqftMultipliers = {
  basic: 8, // $8 per sq ft
  standard: 12, // $12 per sq ft
  premium: 16, // $16 per sq ft
};

// Calculate an estimated project cost based on system selection, quality tier and square footage
export const calculateProjectCost = (
  selectedSystems: Record<string, boolean> | SystemCategories,
  qualityTier: string,
  squareFootage: number
): { 
  totalCost: number;
  breakdown: Record<string, number>;
} => {
  // Ensure we have a valid tier
  const tier = qualityTier in qualityTiers ? qualityTier : "standard";
  
  // Calculate base amount using square footage
  const baseAmount = squareFootage * (sqftMultipliers[tier as keyof typeof sqftMultipliers] || 12);
  
  // Calculate system costs
  let totalCost = 0;
  const breakdown: Record<string, number> = {};
  
  Object.entries(selectedSystems).forEach(([systemId, isSelected]) => {
    if (isSelected) {
      const category = costMatrixCategories.find(cat => cat.id === systemId);
      if (category) {
        // Calculate cost for this system using the matrix pricing
        const systemCost = category.qualityTiers[tier as keyof typeof category.qualityTiers] || category.basePrice;
        
        // For larger homes, scale the cost based on square footage
        const scaleFactor = Math.sqrt(squareFootage / 2500); // Assumes 2500 sqft as baseline
        const adjustedCost = systemCost * scaleFactor;
        
        // Allocate portion of total based on the percentage
        const portionOfTotal = baseAmount * (category.percentOfTotal / 100);
        
        // Use the higher of the calculated costs
        const finalSystemCost = Math.max(adjustedCost, portionOfTotal);
        
        breakdown[systemId] = finalSystemCost;
        totalCost += finalSystemCost;
      }
    }
  });
  
  return {
    totalCost,
    breakdown
  };
};
