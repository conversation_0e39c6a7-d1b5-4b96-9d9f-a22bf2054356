
import { useCallback } from 'react';
import { FormData } from '../types/formTypes';
import { shouldSkipSystemStep } from '../utils/formUtils';

interface UseFormNavigationProps {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  totalBaseSteps: number;
  startAtQualification?: boolean;
}

export const useFormNavigation = ({
  formData,
  setFormData,
  totalBaseSteps,
  startAtQualification = false
}: UseFormNavigationProps) => {
  // Function to get total steps considering conditional logic
  const getTotalSteps = useCallback(() => {
    return totalBaseSteps; // Dynamic based on selections and conditions
  }, [totalBaseSteps]);

  // Initialize at qualification step if specified
  const initializeForm = useCallback(() => {
    if (startAtQualification) {
      setFormData((prev) => ({
        ...prev,
        currentStep: 2, // Qualification is step 2
      }));
    }
  }, [startAtQualification, setFormData]);

  // Navigate to next step
  const nextStep = useCallback(() => {
    setFormData((prev) => {
      // Ensure currentStep is at least 1 (1-based indexing)
      const currentStepNormalized = prev.currentStep || 1;
      const nextStepIndex = currentStepNormalized + 1;
      const totalSteps = getTotalSteps();

      // Convert SystemCategories to Record<string, boolean> for shouldSkipSystemStep
      const systemCategoriesRecord: Record<string, boolean> = {};
      if (prev.systemCategories) {
        Object.entries(prev.systemCategories).forEach(([key, value]) => {
          systemCategoriesRecord[key] = !!value;
        });
      }

      // Check if we should skip the next step
      if (shouldSkipSystemStep(nextStepIndex, systemCategoriesRecord)) {
        return {
          ...prev,
          currentStep: Math.min(nextStepIndex + 1, totalSteps),
        };
      } else {
        return {
          ...prev,
          currentStep: Math.min(nextStepIndex, totalSteps),
        };
      }
    });
  }, [getTotalSteps, setFormData]);

  // Navigate to previous step
  const prevStep = useCallback(() => {
    setFormData((prev) => {
      // Ensure currentStep is at least 1 (1-based indexing)
      const currentStepNormalized = prev.currentStep || 1;
      const prevStepIndex = currentStepNormalized - 1;

      // Convert SystemCategories to Record<string, boolean> for shouldSkipSystemStep
      const systemCategoriesRecord: Record<string, boolean> = {};
      if (prev.systemCategories) {
        Object.entries(prev.systemCategories).forEach(([key, value]) => {
          systemCategoriesRecord[key] = !!value;
        });
      }

      // Check if we should skip the previous step
      if (shouldSkipSystemStep(prevStepIndex, systemCategoriesRecord)) {
        return {
          ...prev,
          currentStep: Math.max(prevStepIndex - 1, 1),
        };
      } else {
        return {
          ...prev,
          currentStep: Math.max(prevStepIndex, 1),
        };
      }
    });
  }, [setFormData]);

  // Go to specific step
  const goToStep = useCallback((step: number) => {
    setFormData((prev) => ({
      ...prev,
      // Ensure step is at least 1 (1-based indexing)
      currentStep: Math.min(Math.max(step, 1), getTotalSteps()),
    }));
  }, [getTotalSteps, setFormData]);

  return {
    getTotalSteps,
    nextStep,
    prevStep,
    goToStep,
    initializeForm,
  };
};
