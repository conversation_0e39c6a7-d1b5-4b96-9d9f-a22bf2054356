import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { 
  ProductPackage, 
  PackageItem, 
  CreatePackageRequest, 
  AddPackageItemRequest,
  UpdatePackageItemRequest,
  PackagePricingCalculation 
} from '@/types/packageTypes';
import { ProductItem } from '@/hooks/usePackageManagement';

export const useCustomPackages = () => {
  const [packages, setPackages] = useState<ProductPackage[]>([]);
  const [currentPackage, setCurrentPackage] = useState<ProductPackage | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const { toast } = useToast();

  // Fetch all packages
  const fetchPackages = async () => {
    try {
      setIsLoading(true);
      
      // Fetch packages with their items
      const { data: packagesData, error: packagesError } = await supabase
        .from('product_packages')
        .select(`
          *,
          product_package_items (
            *,
            product_catalog_items (*)
          )
        `)
        .order('created_at', { ascending: false });

      if (packagesError) {
        console.error('Error fetching packages:', packagesError);
        toast({
          title: "Error",
          description: "Failed to load packages",
          variant: "destructive",
        });
        return;
      }

      // Transform the data to match our interface
      const transformedPackages: ProductPackage[] = packagesData?.map(pkg => ({
        id: pkg.id,
        name: pkg.name,
        description: pkg.description || '',
        systemCategory: pkg.system_category,
        subcategories: pkg.subcategories || [],
        totalCost: pkg.total_cost || 0,
        markupPercentage: pkg.markup_percentage || 0,
        finalPrice: pkg.final_price || 0,
        isDefault: pkg.is_default || false,
        createdBy: pkg.created_by || '',
        teamId: pkg.team_id || '',
        createdAt: pkg.created_at,
        updatedAt: pkg.updated_at,
        items: pkg.product_package_items?.map((item: any) => ({
          id: item.id,
          packageId: item.package_id,
          productId: item.product_id,
          product: {
            id: item.product_catalog_items.id,
            name: item.product_catalog_items.name,
            description: item.product_catalog_items.description || '',
            model: item.product_catalog_items.model || '',
            manufacturer: item.product_catalog_items.manufacturer || '',
            systemCategory: item.product_catalog_items.system_category,
            unitCost: item.product_catalog_items.unit_cost,
            quantity: item.product_catalog_items.quantity,
            unit: item.product_catalog_items.unit,
            subcategories: item.product_catalog_items.subcategories || [],
            sku: item.product_catalog_items.sku_number || '',
            imageUrl: item.product_catalog_items.image_url || '',
            msrp: item.product_catalog_items.msrp || 0,
            tradePrice: item.product_catalog_items.trade_price || 0,
            documentUrl: item.product_catalog_items.document_url || '',
            documentName: item.product_catalog_items.document_name || '',
            isDefault: item.product_catalog_items.is_default || false,
          } as ProductItem,
          quantity: item.quantity,
          customPrice: item.custom_price,
          notes: item.notes || '',
          createdAt: item.created_at,
          updatedAt: item.updated_at,
        })) || []
      })) || [];

      setPackages(transformedPackages);
    } catch (error) {
      console.error('Error fetching packages:', error);
      toast({
        title: "Error",
        description: "Failed to load packages",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new package
  const createPackage = async (packageData: CreatePackageRequest): Promise<string | null> => {
    try {
      setIsCreating(true);

      const { data, error } = await supabase
        .from('product_packages')
        .insert({
          name: packageData.name,
          description: packageData.description,
          system_category: packageData.systemCategory,
          subcategories: packageData.subcategories || [],
          markup_percentage: packageData.markupPercentage || 0,
          is_default: packageData.isDefault || false,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating package:', error);
        toast({
          title: "Error",
          description: "Failed to create package",
          variant: "destructive",
        });
        return null;
      }

      toast({
        title: "Success",
        description: `Package "${packageData.name}" created successfully`,
      });

      // Refresh packages list
      await fetchPackages();
      
      return data.id;
    } catch (error) {
      console.error('Error creating package:', error);
      toast({
        title: "Error",
        description: "Failed to create package",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  // Add item to package
  const addItemToPackage = async (itemData: AddPackageItemRequest): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('product_package_items')
        .insert({
          package_id: itemData.packageId,
          product_id: itemData.productId,
          quantity: itemData.quantity,
          custom_price: itemData.customPrice,
          notes: itemData.notes,
        });

      if (error) {
        console.error('Error adding item to package:', error);
        toast({
          title: "Error",
          description: "Failed to add item to package",
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Success",
        description: "Item added to package successfully",
      });

      // Refresh packages list
      await fetchPackages();
      
      return true;
    } catch (error) {
      console.error('Error adding item to package:', error);
      toast({
        title: "Error",
        description: "Failed to add item to package",
        variant: "destructive",
      });
      return false;
    }
  };

  // Update package item
  const updatePackageItem = async (itemData: UpdatePackageItemRequest): Promise<boolean> => {
    try {
      const updateData: any = {};
      if (itemData.quantity !== undefined) updateData.quantity = itemData.quantity;
      if (itemData.customPrice !== undefined) updateData.custom_price = itemData.customPrice;
      if (itemData.notes !== undefined) updateData.notes = itemData.notes;

      const { error } = await supabase
        .from('product_package_items')
        .update(updateData)
        .eq('id', itemData.id);

      if (error) {
        console.error('Error updating package item:', error);
        toast({
          title: "Error",
          description: "Failed to update package item",
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Success",
        description: "Package item updated successfully",
      });

      // Refresh packages list
      await fetchPackages();
      
      return true;
    } catch (error) {
      console.error('Error updating package item:', error);
      toast({
        title: "Error",
        description: "Failed to update package item",
        variant: "destructive",
      });
      return false;
    }
  };

  // Remove item from package
  const removeItemFromPackage = async (itemId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('product_package_items')
        .delete()
        .eq('id', itemId);

      if (error) {
        console.error('Error removing item from package:', error);
        toast({
          title: "Error",
          description: "Failed to remove item from package",
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Success",
        description: "Item removed from package successfully",
      });

      // Refresh packages list
      await fetchPackages();
      
      return true;
    } catch (error) {
      console.error('Error removing item from package:', error);
      toast({
        title: "Error",
        description: "Failed to remove item from package",
        variant: "destructive",
      });
      return false;
    }
  };

  // Delete package
  const deletePackage = async (packageId: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('product_packages')
        .delete()
        .eq('id', packageId);

      if (error) {
        console.error('Error deleting package:', error);
        toast({
          title: "Error",
          description: "Failed to delete package",
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Success",
        description: "Package deleted successfully",
      });

      // Refresh packages list
      await fetchPackages();
      
      return true;
    } catch (error) {
      console.error('Error deleting package:', error);
      toast({
        title: "Error",
        description: "Failed to delete package",
        variant: "destructive",
      });
      return false;
    }
  };

  // Calculate package pricing
  const calculatePackagePricing = (pkg: ProductPackage): PackagePricingCalculation => {
    const itemBreakdown = pkg.items.map(item => {
      const unitPrice = item.customPrice || item.product.unitCost;
      const lineTotal = unitPrice * item.quantity;
      
      return {
        productId: item.productId,
        productName: item.product.name,
        quantity: item.quantity,
        unitPrice: item.product.unitCost,
        customPrice: item.customPrice,
        lineTotal,
      };
    });

    const subtotal = itemBreakdown.reduce((sum, item) => sum + item.lineTotal, 0);
    const markupAmount = subtotal * (pkg.markupPercentage / 100);
    const finalPrice = subtotal + markupAmount;

    return {
      subtotal,
      markup: pkg.markupPercentage,
      markupAmount,
      finalPrice,
      itemBreakdown,
    };
  };

  // Get packages by category
  const getPackagesByCategory = (category: string) => {
    return packages.filter(pkg => pkg.systemCategory === category);
  };

  // Search packages
  const searchPackages = (searchTerm: string) => {
    return packages.filter(pkg => 
      pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.items.some(item => 
        item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.product.sku.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  };

  // Load packages on mount
  useEffect(() => {
    fetchPackages();
  }, []);

  return {
    packages,
    currentPackage,
    isLoading,
    isCreating,
    setCurrentPackage,
    fetchPackages,
    createPackage,
    addItemToPackage,
    updatePackageItem,
    removeItemFromPackage,
    deletePackage,
    calculatePackagePricing,
    getPackagesByCategory,
    searchPackages,
  };
};
