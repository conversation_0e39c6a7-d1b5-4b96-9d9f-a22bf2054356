import { useState, useEffect } from 'react';
import { defaultSubcategories, type DefaultSubcategory } from '@/data/defaultSubcategories';

export interface CustomSubcategory {
  id: string;
  name: string;
  systemCategory: string;
  order: number;
  description?: string;
  isCustom: true;
  isEnabled: boolean;
  createdAt: string;
}

export interface SubcategoryPreference {
  id: string;
  isEnabled: boolean;
}

export interface SubcategoryPreferences {
  defaultSubcategories: Record<string, boolean>; // subcategory name -> enabled state
  customSubcategories: CustomSubcategory[];
}

const STORAGE_KEY = 'subcategory-preferences';

const getDefaultPreferences = (): SubcategoryPreferences => {
  const defaultPrefs: Record<string, boolean> = {};
  
  // Enable all default subcategories by default
  defaultSubcategories.forEach(sub => {
    const key = `${sub.systemCategory}-${sub.name}`;
    defaultPrefs[key] = true;
  });

  return {
    defaultSubcategories: defaultPrefs,
    customSubcategories: []
  };
};

export const useSubcategoryPreferences = () => {
  const [preferences, setPreferences] = useState<SubcategoryPreferences>(getDefaultPreferences());
  const [isLoading, setIsLoading] = useState(true);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedPrefs = JSON.parse(stored);
        // Merge with defaults to ensure new default subcategories are included
        const defaultPrefs = getDefaultPreferences();
        setPreferences({
          defaultSubcategories: {
            ...defaultPrefs.defaultSubcategories,
            ...parsedPrefs.defaultSubcategories
          },
          customSubcategories: parsedPrefs.customSubcategories || []
        });
      }
    } catch (error) {
      console.error('Error loading subcategory preferences:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save preferences to localStorage whenever they change
  const savePreferences = (newPreferences: SubcategoryPreferences) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(newPreferences));
      setPreferences(newPreferences);
    } catch (error) {
      console.error('Error saving subcategory preferences:', error);
    }
  };

  // Toggle a default subcategory's enabled state
  const toggleDefaultSubcategory = (systemCategory: string, name: string) => {
    const key = `${systemCategory}-${name}`;
    const newPreferences = {
      ...preferences,
      defaultSubcategories: {
        ...preferences.defaultSubcategories,
        [key]: !preferences.defaultSubcategories[key]
      }
    };
    savePreferences(newPreferences);
  };

  // Add a custom subcategory
  const addCustomSubcategory = (subcategory: Omit<CustomSubcategory, 'id' | 'isCustom' | 'createdAt'>) => {
    const newCustomSubcategory: CustomSubcategory = {
      ...subcategory,
      id: crypto.randomUUID(),
      isCustom: true,
      createdAt: new Date().toISOString()
    };

    const newPreferences = {
      ...preferences,
      customSubcategories: [...preferences.customSubcategories, newCustomSubcategory]
    };
    savePreferences(newPreferences);
  };

  // Update a custom subcategory
  const updateCustomSubcategory = (id: string, updates: Partial<CustomSubcategory>) => {
    const newPreferences = {
      ...preferences,
      customSubcategories: preferences.customSubcategories.map(sub =>
        sub.id === id ? { ...sub, ...updates } : sub
      )
    };
    savePreferences(newPreferences);
  };

  // Delete a custom subcategory
  const deleteCustomSubcategory = (id: string) => {
    const newPreferences = {
      ...preferences,
      customSubcategories: preferences.customSubcategories.filter(sub => sub.id !== id)
    };
    savePreferences(newPreferences);
  };

  // Get enabled subcategories for a system category
  const getEnabledSubcategoriesForSystem = (systemCategory: string) => {
    const enabledDefaults = defaultSubcategories
      .filter(sub => {
        const key = `${sub.systemCategory}-${sub.name}`;
        return sub.systemCategory === systemCategory && preferences.defaultSubcategories[key];
      })
      .map(sub => ({
        id: `default-${sub.systemCategory}-${sub.name.toLowerCase().replace(/\s+/g, '-')}`,
        name: sub.name,
        systemCategory: sub.systemCategory,
        order: sub.order,
        description: sub.description,
        isDefault: true,
        isCustom: false,
        isEnabled: true
      }));

    const enabledCustom = preferences.customSubcategories
      .filter(sub => sub.systemCategory === systemCategory && sub.isEnabled)
      .map(sub => ({
        id: sub.id,
        name: sub.name,
        systemCategory: sub.systemCategory,
        order: sub.order,
        description: sub.description,
        isDefault: false,
        isCustom: true,
        isEnabled: true
      }));

    return [...enabledDefaults, ...enabledCustom].sort((a, b) => a.order - b.order);
  };

  // Get all subcategories (enabled and disabled) for management interface
  const getAllSubcategoriesForSystem = (systemCategory: string) => {
    const allDefaults = defaultSubcategories
      .filter(sub => sub.systemCategory === systemCategory)
      .map(sub => {
        const key = `${sub.systemCategory}-${sub.name}`;
        return {
          id: `default-${sub.systemCategory}-${sub.name.toLowerCase().replace(/\s+/g, '-')}`,
          name: sub.name,
          systemCategory: sub.systemCategory,
          order: sub.order,
          description: sub.description,
          isDefault: true,
          isCustom: false,
          isEnabled: preferences.defaultSubcategories[key] ?? true
        };
      });

    const allCustom = preferences.customSubcategories
      .filter(sub => sub.systemCategory === systemCategory)
      .map(sub => ({
        id: sub.id,
        name: sub.name,
        systemCategory: sub.systemCategory,
        order: sub.order,
        description: sub.description,
        isDefault: false,
        isCustom: true,
        isEnabled: sub.isEnabled
      }));

    return [...allDefaults, ...allCustom].sort((a, b) => a.order - b.order);
  };

  // Check if a default subcategory is enabled
  const isDefaultSubcategoryEnabled = (systemCategory: string, name: string) => {
    const key = `${systemCategory}-${name}`;
    return preferences.defaultSubcategories[key] ?? true;
  };

  // Reset all preferences to defaults
  const resetToDefaults = () => {
    const defaultPrefs = getDefaultPreferences();
    savePreferences(defaultPrefs);
  };

  return {
    preferences,
    isLoading,
    toggleDefaultSubcategory,
    addCustomSubcategory,
    updateCustomSubcategory,
    deleteCustomSubcategory,
    getEnabledSubcategoriesForSystem,
    getAllSubcategoriesForSystem,
    isDefaultSubcategoryEnabled,
    resetToDefaults
  };
};
