
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface ProductItem {
  id: string;
  name: string;
  description: string;
  model: string;
  manufacturer: string;
  systemCategory: string;
  unitCost: number;
  quantity: number;
  unit: string;
  subcategories: string[];
  sku: string;
  imageUrl: string;
  msrp: number;
  tradePrice?: number;
  documentUrl?: string;
  documentName?: string;
  isDefault: boolean;
}

export interface CategoryItem {
  id: string;
  name: string;
  system_category: string;
}

// Export systemCategories directly from the file
export const systemCategories = {
  lighting: 'Lighting',
  shades: 'Shades',
  audio: 'Audio',
  video: 'Video',
  network: 'Network',
  security: 'Security',
  controlSystems: 'Control Systems',
};

const usePackageManagement = (options: { isStandalone?: boolean } = {}) => {
  const [items, setItems] = useState<ProductItem[]>([]);
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [currentItem, setCurrentItem] = useState<ProductItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isImportCSVDialogOpen, setIsImportCSVDialogOpen] = useState(false);

  // Default products toggle state
  const [showDefaultProducts, setShowDefaultProducts] = useState(() => {
    const saved = localStorage.getItem('showDefaultProducts');
    return saved !== null ? JSON.parse(saved) : true;
  });

  const [systemCategoriesState, setSystemCategoriesState] = useState<Record<string, string>>({});
  const { toast } = useToast();

  useEffect(() => {
    fetchItems();
    loadSystemCategories();
    fetchCategories();
    initializeDefaultProducts();
  }, []);

  // Save showDefaultProducts preference to localStorage
  useEffect(() => {
    localStorage.setItem('showDefaultProducts', JSON.stringify(showDefaultProducts));
  }, [showDefaultProducts]);

  const fetchItems = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('product_catalog_items')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching items:', error);
        return;
      }

      if (data) {
        // Map database items to our format
        const mappedItems = data.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || '',
          model: item.model || '',
          manufacturer: item.manufacturer || '',
          systemCategory: item.system_category,
          unitCost: item.unit_cost || 0,
          quantity: item.quantity || 1,
          unit: item.unit || 'piece',
          subcategories: item.subcategories || [],
          sku: item.sku_number || '',
          imageUrl: item.image_url || '',
          msrp: item.msrp || 0,
          tradePrice: item.trade_price || 0,
          documentUrl: item.document_url || '',
          documentName: item.document_name || '',
          isDefault: item.is_default || false,
        }));
        setItems(mappedItems);
      }
    } catch (error) {
      console.error('Error fetching items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // We need to modify the loadSystemCategories function to use our exported systemCategories
  const loadSystemCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('system_category, name')
        .order('name');

      if (!error && data) {
        // We'll still load dynamic categories from the database, but use our defaults as fallback
        const loadedCategories: Record<string, string> = {...systemCategories};
        data.forEach(item => {
          loadedCategories[item.system_category] = item.name;
        });
        // No need to set state for systemCategories anymore as it's a constant export
      }
    } catch (error) {
      console.error('Error loading system categories:', error);
    }
  };

  const handleAddItem = () => {
    setCurrentItem({
      id: crypto.randomUUID(),
      name: '',
      description: '',
      model: '',
      manufacturer: '',
      systemCategory: 'lighting',
      unitCost: 0,
      quantity: 1,
      unit: 'piece',
      subcategories: [],
      sku: '',
      imageUrl: '',
      msrp: 0,
      tradePrice: 0,
      documentUrl: '',
      documentName: '',
      isDefault: true,
    });
    setIsAddDialogOpen(true);
  };

  const handleEditItem = (item: ProductItem) => {
    setCurrentItem(item);
    setIsEditDialogOpen(true);
  };

  const handleDeleteItem = async (item: ProductItem) => {
    try {
      const { error } = await supabase
        .from('product_catalog_items')
        .delete()
        .eq('id', item.id);

      if (error) {
        console.error('Error deleting item:', error);
        return;
      }

      setItems(prev => prev.filter(i => i.id !== item.id));
      toast({
        title: "Product Deleted",
        description: `${item.name} has been removed from the catalog.`,
      })
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const handleUpdateItem = (field: string, value: any) => {
    setCurrentItem(prev => {
      if (prev) {
        return { ...prev, [field]: value };
      }
      return prev;
    });
  };

  const updateItemSubcategories = async (subcategories: string[]) => {
    if (currentItem) {
      try {
        const { data, error } = await supabase
          .from('product_catalog_items')
          .update({ subcategories: subcategories })
          .eq('id', currentItem.id)
          .select();

        if (error) {
          console.error('Error updating subcategories:', error);
          return;
        }

        setCurrentItem(prev => {
          if (prev) {
            return { ...prev, subcategories: subcategories };
          }
          return prev;
        });

        setItems(prev => {
          return prev.map(item => {
            if (item.id === currentItem.id) {
              return { ...item, subcategories: subcategories };
            }
            return item;
          });
        });
      } catch (error) {
        console.error('Error updating subcategories:', error);
      }
    }
  };

  const handleSaveNewItem = async (newItem: ProductItem) => {
    try {
      const { data, error } = await supabase
        .from('product_catalog_items')
        .insert([
          {
            id: newItem.id,
            name: newItem.name,
            description: newItem.description,
            model: newItem.model,
            manufacturer: newItem.manufacturer,
            system_category: newItem.systemCategory,
            unit_cost: newItem.unitCost,
            quantity: newItem.quantity,
            unit: newItem.unit,
            subcategories: newItem.subcategories,
            sku_number: newItem.sku,
            image_url: newItem.imageUrl,
            msrp: newItem.msrp,
            trade_price: newItem.tradePrice,
            document_url: newItem.documentUrl,
            document_name: newItem.documentName,
            is_default: newItem.isDefault,
          }
        ])
        .select();

      if (error) {
        console.error('Error saving new item:', error);
        return;
      }

      setItems(prev => [...prev, newItem]);
      setIsAddDialogOpen(false);
      toast({
        title: "Product Created",
        description: `${newItem.name} has been added to the catalog.`,
      })
    } catch (error) {
      console.error('Error saving new item:', error);
    }
  };

  const handleSaveExistingItem = async (updatedItem?: ProductItem) => {
    if (!currentItem && !updatedItem) {
      console.error('No current item to update.');
      return;
    }

    const itemToUpdate = updatedItem || currentItem;

    try {
      const { data, error } = await supabase
        .from('product_catalog_items')
        .update({
          name: itemToUpdate.name,
          description: itemToUpdate.description,
          model: itemToUpdate.model,
          manufacturer: itemToUpdate.manufacturer,
          system_category: itemToUpdate.systemCategory,
          unit_cost: itemToUpdate.unitCost,
          quantity: itemToUpdate.quantity,
          unit: itemToUpdate.unit,
          subcategories: itemToUpdate.subcategories,
          sku_number: itemToUpdate.sku,
          image_url: itemToUpdate.imageUrl,
          msrp: itemToUpdate.msrp,
          trade_price: itemToUpdate.tradePrice,
          document_url: itemToUpdate.documentUrl,
          document_name: itemToUpdate.documentName,
          is_default: itemToUpdate.isDefault,
        })
        .eq('id', itemToUpdate.id)
        .select();

      if (error) {
        console.error('Error updating item:', error);
        return;
      }

      setItems(prev =>
        prev.map(item => (item.id === itemToUpdate.id ? itemToUpdate : item))
      );
      setCurrentItem(itemToUpdate);
      setIsEditDialogOpen(false);
      toast({
        title: "Product Updated",
        description: `${itemToUpdate.name} has been updated in the catalog.`,
      })
    } catch (error) {
      console.error('Error updating item:', error);
    }
  };





  const getItemsByCategory = (category: string, subcategory?: string) => {
    if (subcategory) {
      return items.filter(item => item.systemCategory === category && item.subcategories.includes(subcategory));
    }
    return items.filter(item => item.systemCategory === category);
  };

  const getAllItems = () => {
    return items;
  };

  const getItemsBySubcategory = (subcategory: string) => {
    return items.filter(item => item.subcategories.includes(subcategory));
  };

  // Update CSV import handler to include field mapping
  const handleImportCSVContent = async (csvContent: string, fieldMapping?: Record<string, string>) => {
    try {
      // Parse CSV
      const lines = csvContent.trim().split('\n');
      const headers = parseCSVLine(lines[0]);

      // Function to parse CSV lines properly, handling quoted values
      function parseCSVLine(line: string): string[] {
        const result: string[] = [];
        let inQuote = false;
        let currentValue = '';

        for (let i = 0; i < line.length; i++) {
          const char = line[i];

          if (char === '"' && (i === 0 || line[i-1] !== '\\')) {
            inQuote = !inQuote;
          } else if (char === ',' && !inQuote) {
            result.push(currentValue.trim());
            currentValue = '';
          } else {
            currentValue += char;
          }
        }

        if (currentValue) {
          result.push(currentValue.trim());
        }

        return result;
      }

      // Process each row from CSV
      const newItems: any[] = [];
      for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue;

        const values = parseCSVLine(lines[i]);

        // Pad values array to match headers length if needed (handle missing columns)
        while (values.length < headers.length) {
          values.push('');
        }

        // Trim values array if it's longer than headers (handle extra columns)
        if (values.length > headers.length) {
          values.splice(headers.length);
        }

        // Create object with default field mapping
        let item: Record<string, any> = {
          id: crypto.randomUUID(),
          unit: 'piece',
          unit_cost: 0,
          quantity: 1,
          discontinued: false,
          is_default: false,
        };

        // Apply custom field mappings if provided
        if (fieldMapping) {
          headers.forEach((header, index) => {
            const dbField = fieldMapping[header];
            if (dbField && index < values.length) {
              const value = values[index]?.trim();

              // Skip empty values except for boolean fields
              if (!value && dbField !== 'discontinued') {
                return;
              }

              // Process numeric fields
              if (['unit_cost', 'trade_price', 'msrp'].includes(dbField)) {
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                  item[dbField] = numValue;
                }
              }
              // Process boolean fields
              else if (dbField === 'discontinued') {
                item[dbField] = value?.toLowerCase() === 'true';
              }
              // Process subcategories as array
              else if (dbField === 'subcategories') {
                if (value) {
                  item[dbField] = value.split(';').map(s => s.trim());
                }
              }
              else {
                if (value) {
                  item[dbField] = value;
                }
              }
            }
          });

          // Map special fields using traditional fixed positions
          if (!fieldMapping['name']) {
            const nameIndex = headers.findIndex(h =>
              h.toLowerCase().includes('name') ||
              h.toLowerCase().includes('description')
            );
            if (nameIndex !== -1 && nameIndex < values.length && values[nameIndex]?.trim()) {
              item.name = values[nameIndex].trim();
            }
          }

          if (!fieldMapping['system_category']) {
            const categoryIndex = headers.findIndex(h =>
              h.toLowerCase().includes('category')
            );
            if (categoryIndex !== -1 && categoryIndex < values.length && values[categoryIndex]?.trim()) {
              item.system_category = values[categoryIndex].trim();
            }
          }
        }
        // Traditional fixed mapping (compatibility with old code)
        else {
          // Look for standard expected columns
          headers.forEach((header, index) => {
            if (index >= values.length) return; // Skip if no value for this column

            const headerLower = header.toLowerCase();
            const value = values[index]?.trim();

            if (headerLower === 'sku' || headerLower.includes('sku number')) {
              if (value) item.sku_number = value;
            }
            else if (headerLower === 'description' || headerLower.includes('product name') || headerLower.includes('name')) {
              if (value) item.name = value;
            }
            else if (headerLower.includes('long description')) {
              if (value) item.description = value;
            }
            else if (headerLower === 'model') {
              if (value) item.model = value;
            }
            else if (headerLower.includes('category group')) {
              // Skip category group for now
            }
            else if (headerLower === 'category' || headerLower.includes('system')) {
              if (value) item.system_category = value;
            }
            else if (headerLower === 'manufacturer') {
              if (value) item.manufacturer = value;
            }
            else if (headerLower.includes('manufacturer sku')) {
              if (value) item.manufacturer_sku = value;
            }
            else if (headerLower.includes('image url')) {
              if (value) item.image_url = value;
            }
            else if (headerLower.includes('document name')) {
              if (value) item.document_name = value;
            }
            else if (headerLower.includes('document url')) {
              if (value) item.document_url = value;
            }
            else if (headerLower.includes('unit of measure') || headerLower === 'unit') {
              item.unit = value || 'piece';
            }
            else if (headerLower.includes('buy cost') || headerLower.includes('cost') || headerLower === 'price') {
              const numValue = parseFloat(value);
              if (!isNaN(numValue)) item.unit_cost = numValue;
            }
            else if (headerLower.includes('trade price')) {
              const numValue = parseFloat(value);
              if (!isNaN(numValue)) item.trade_price = numValue;
            }
            else if (headerLower.includes('msrp')) {
              const numValue = parseFloat(value);
              if (!isNaN(numValue)) item.msrp = numValue;
            }
            else if (headerLower === 'ean') {
              if (value) item.ean = value;
            }
            else if (headerLower === 'discontinued') {
              item.discontinued = value?.toLowerCase() === 'true';
            }
          });
        }

        // Validate required fields - only SKU is required
        if (item.sku_number) {
          // Set default values for missing fields
          if (!item.name) {
            item.name = item.sku_number; // Use SKU as name if no name provided
          }
          if (!item.system_category) {
            item.system_category = 'general'; // Default category
          }
          if (!item.unit_cost) {
            item.unit_cost = 0; // Default cost
          }
          newItems.push(item);
        } else {
          console.log('Skipping item due to missing SKU:', item);
        }
      }

      // Insert items into database
      if (newItems.length > 0) {
        const { data, error } = await supabase
          .from('product_catalog_items')
          .insert(newItems)
          .select();

        if (error) {
          console.error('Error inserting items:', error);
          throw new Error(`Failed to insert items: ${error.message}`);
        }

        // Add the new items to our state
        if (data) {
          // Map database items to our format
          const mappedItems = data.map((item: any) => ({
            id: item.id,
            name: item.name,
            description: item.description || '',
            model: item.model || '',
            manufacturer: item.manufacturer || '',
            systemCategory: item.system_category,
            unitCost: item.unit_cost || 0,
            quantity: item.quantity || 1,
            unit: item.unit || 'piece',
            subcategories: item.subcategories || [],
            sku: item.sku_number || '',
            imageUrl: item.image_url || '',
            msrp: item.msrp || 0,
            isDefault: item.is_default || false,
          }));

          setItems(prev => [...prev, ...mappedItems]);
        }

        return newItems.length;
      }

      return 0;
    } catch (error) {
      console.error('Error importing CSV:', error);
      throw error;
    }
  };

  // Mark existing products as default products
  const initializeDefaultProducts = async () => {
    try {
      // Check if any products are already marked as default
      const { data: existingDefaults, error: checkError } = await supabase
        .from('product_catalog_items')
        .select('id')
        .eq('is_default', true)
        .limit(1);

      if (checkError) {
        console.error('Error checking for default products:', checkError);
        return;
      }

      // If no default products exist, mark all current products as defaults
      if (!existingDefaults || existingDefaults.length === 0) {
        const { error: updateError } = await supabase
          .from('product_catalog_items')
          .update({ is_default: true })
          .or('is_default.is.null,is_default.eq.false'); // Update products where is_default is null or false

        if (updateError) {
          console.error('Error marking existing products as defaults:', updateError);
        } else {
          console.log('Existing products marked as defaults successfully');
          // Refresh items to reflect the changes
          fetchItems();
        }
      }
    } catch (error) {
      console.error('Error initializing default products:', error);
    }
  };

  // Toggle default products visibility
  const toggleDefaultProducts = () => {
    setShowDefaultProducts(prev => !prev);
  };

  // Filter items based on default products toggle
  const getFilteredItems = () => {
    if (showDefaultProducts) {
      return items;
    } else {
      return items.filter(item => !item.isDefault);
    }
  };

  // Override existing functions to use filtered items
  const getFilteredItemsByCategory = (category: string, subcategory?: string) => {
    const filteredItems = getFilteredItems();
    if (subcategory) {
      return filteredItems.filter(item => item.systemCategory === category && item.subcategories.includes(subcategory));
    }
    return filteredItems.filter(item => item.systemCategory === category);
  };

  const getFilteredItemsBySubcategory = (subcategory: string) => {
    const filteredItems = getFilteredItems();
    return filteredItems.filter(item => item.subcategories.includes(subcategory));
  };

  return {
    items: getFilteredItems(), // Return filtered items based on toggle
    allItems: items, // Expose all items for cases where filtering is not needed
    // For backward compatibility, expose items as packages and products too
    packages: getFilteredItems(),
    products: getFilteredItems(),
    categories,
    currentItem,
    isLoading,
    isAddDialogOpen,
    isEditDialogOpen,
    isImportCSVDialogOpen,
    showDefaultProducts,
    systemCategories, // Make sure we return the systemCategories in the hook result for backward compatibility
    handleAddItem,
    handleEditItem,
    handleDeleteItem,
    handleUpdateItem,
    handleSaveNewItem,
    handleSaveExistingItem,
    handleImportCSVContent,
    updateItemSubcategories,
    getItemsByCategory: getFilteredItemsByCategory,
    getAllItems: getFilteredItems,
    getItemsBySubcategory: getFilteredItemsBySubcategory,
    toggleDefaultProducts,
    setIsAddDialogOpen,
    setIsEditDialogOpen,
    setIsImportCSVDialogOpen
  };
};

export default usePackageManagement;
