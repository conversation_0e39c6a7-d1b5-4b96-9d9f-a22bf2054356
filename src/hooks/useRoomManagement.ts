
import { useCallback } from 'react';
import { FormData } from '../types/formTypes';
import { RoomConfig } from '../types/roomTypes';
import { createNewRoom } from '../utils/formUtils';

interface UseRoomManagementProps {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
}

export const useRoomManagement = ({ formData, setFormData }: UseRoomManagementProps) => {
  // Add a new room with a specified name and apply system settings if provided
  const addRoom = useCallback((roomName: string = "Untitled Room", systemSettings?: Record<string, boolean>) => {
    const newRoom = createNewRoom(roomName, systemSettings);
    
    setFormData((prev) => ({
      ...prev,
      rooms: [...prev.rooms, newRoom],
    }));
  }, [setFormData]);

  // Update a room
  const updateRoom = useCallback((id: string, data: Partial<RoomConfig>) => {
    setFormData((prev) => ({
      ...prev,
      rooms: prev.rooms.map((room) =>
        room.id === id ? { ...room, ...data } : room
      ),
    }));
  }, [setFormData]);

  // Remove a room
  const removeRoom = useCallback((id: string) => {
    setFormData((prev) => ({
      ...prev,
      rooms: prev.rooms.filter((room) => room.id !== id),
    }));
  }, [setFormData]);

  return {
    addRoom,
    updateRoom,
    removeRoom,
  };
};
