
import { useCallback } from 'react';
import { FormData } from '../types/formTypes';
import { getWalkthroughById } from '../services/walkthroughService';
import { initialFormData } from '../context/initialFormData';

interface UseWalkthroughManagementProps {
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  setCurrentWalkthroughId: React.Dispatch<React.SetStateAction<string | null>>;
}

export const useWalkthroughManagement = ({ 
  setFormData, 
  setCurrentWalkthroughId 
}: UseWalkthroughManagementProps) => {
  // Load a saved walkthrough by ID
  const loadSavedWalkthrough = useCallback(async (id: string) => {
    try {
      const walkthrough = await getWalkthroughById(id);
      
      if (walkthrough && walkthrough.form_data) {
        // Make sure form_data has all required fields
        const enhancedFormData = {
          ...initialFormData,
          ...walkthrough.form_data,
          clientInfo: {
            ...initialFormData.clientInfo,
            ...walkthrough.form_data.clientInfo,
          },
          qualificationInfo: {
            ...initialFormData.qualificationInfo,
            ...walkthrough.form_data.qualificationInfo,
          },
          systemCategories: {
            ...initialFormData.systemCategories,
            ...walkthrough.form_data.systemCategories,
          },
        };
        
        setFormData(enhancedFormData);
        setCurrentWalkthroughId(id);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error loading walkthrough:", error);
      return false;
    }
  }, [setFormData, setCurrentWalkthroughId]);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setCurrentWalkthroughId(null);
  }, [setFormData, setCurrentWalkthroughId]);

  return {
    loadSavedWalkthrough,
    resetForm,
  };
};
