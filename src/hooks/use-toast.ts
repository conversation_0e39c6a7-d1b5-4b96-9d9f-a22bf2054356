
import { toast as sonnerToast } from "sonner";

// Simplified toast interface for Sonner
interface Toast {
  title?: React.ReactNode;
  description?: React.ReactNode;
  variant?: "default" | "destructive";
}

// Simplified toast function using only Sonner
function toast(props: Toast) {
  // Use sonner toast to show toasts
  if (props.variant === 'destructive') {
    sonnerToast.error(props.title as string, {
      description: props.description as string,
    });
  } else {
    sonnerToast.success(props.title as string, {
      description: props.description as string,
    });
  }

  return {
    id: Math.random().toString(),
    dismiss: () => {},
    update: () => {},
  };
}

function useToast() {
  return {
    toast,
    dismiss: () => {},
    toasts: [],
  };
}

export { useToast, toast };
