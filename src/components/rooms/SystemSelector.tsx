
import React, { useEffect, useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { RoomConfig } from '@/types/roomTypes';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  LightbulbIcon,
  PanelTopIcon,
  SpeakerIcon,
  MonitorIcon,
  WifiIcon,
  ShieldIcon,
  MonitorSmartphone // Using MonitorSmartphone instead of RemoteControl which doesn't exist
} from 'lucide-react';
import usePackageManagement from '@/hooks/usePackageManagement';

interface SystemSelectorProps {
  roomSettings: RoomConfig;
  roomId: string;
  onUpdate: (id: string, data: Partial<RoomConfig>) => void;
}

// Helper functions for consistent styling
const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'lighting':
      return <LightbulbIcon className="h-5 w-5" />;
    case 'shades':
      return <PanelTopIcon className="h-5 w-5" />;
    case 'audio':
      return <SpeakerIcon className="h-5 w-5" />;
    case 'video':
      return <MonitorIcon className="h-5 w-5" />;
    case 'network':
      return <WifiIcon className="h-5 w-5" />;
    case 'security':
      return <ShieldIcon className="h-5 w-5" />;
    case 'control':
      return <MonitorSmartphone className="h-5 w-5" />;
    default:
      return <LightbulbIcon className="h-5 w-5" />;
  }
};

const getCategoryLabel = (category: string) => {
  switch (category) {
    case 'lighting':
      return 'Lighting';
    case 'shades':
      return 'Shades';
    case 'audio':
      return 'Audio';
    case 'video':
      return 'Video';
    case 'network':
      return 'Network';
    case 'security':
      return 'Security';
    case 'control':
      return 'Control Systems';
    default:
      return category.charAt(0).toUpperCase() + category.slice(1);
  }
};

const SystemSelector: React.FC<SystemSelectorProps> = ({
  roomSettings,
  roomId,
  onUpdate
}) => {
  const [isLightingEnabled, setIsLightingEnabled] = useState(roomSettings.lighting || false);
  const [shadesOption, setShadesOption] = useState<"none" | "manual" | "motorized">(roomSettings.shades || 'none');
  const [audioOption, setAudioOption] = useState<"none" | "hidden" | "in-ceiling">(roomSettings.audio || 'none');
  const [videoOption, setVideoOption] = useState<"none" | "display" | "projector">(roomSettings.video || 'none');
  const [isNetworkEnabled, setIsNetworkEnabled] = useState(roomSettings.network === 'high density' || false);
  const [isSecurityEnabled, setIsSecurityEnabled] = useState(roomSettings.surveillance || false);
  const [controlOption, setControlOption] = useState<"none" | "wall keypad" | "remote" | "app">(roomSettings.control || 'none');

  useEffect(() => {
    setIsLightingEnabled(roomSettings.lighting || false);
    setShadesOption(roomSettings.shades || 'none');
    setAudioOption(roomSettings.audio || 'none');
    setVideoOption(roomSettings.video || 'none');
    setIsNetworkEnabled(roomSettings.network === 'high density' || false);
    setIsSecurityEnabled(roomSettings.surveillance || false);
    setControlOption(roomSettings.control || 'none');
  }, [roomSettings]);

  const handleSystemToggle = (systemType: string, enabled: boolean) => {
    switch (systemType) {
      case 'lighting':
        onUpdate(roomId, { lighting: enabled });
        break;
      case 'network':
        onUpdate(roomId, { network: enabled ? 'high density' : 'standard' });
        break;
      case 'security':
        onUpdate(roomId, { surveillance: enabled });
        break;
      default:
        break;
    }
  };

  const handleSystemOptionChange = (systemType: string, option: string) => {
    switch (systemType) {
      case 'shades':
        // Safe type casting with validation
        if (option === "none" || option === "manual" || option === "motorized") {
          setShadesOption(option);
          onUpdate(roomId, { shades: option });
        }
        break;
      case 'audio':
        // Safe type casting with validation
        if (option === "none" || option === "hidden" || option === "in-ceiling") {
          setAudioOption(option);
          onUpdate(roomId, { audio: option });
        }
        break;
      case 'video':
        // Safe type casting with validation
        if (option === "none" || option === "display" || option === "projector") {
          setVideoOption(option);
          onUpdate(roomId, { video: option });
        }
        break;
      case 'control':
        // Safe type casting with validation
        if (option === "none" || option === "wall keypad" || option === "remote" || option === "app") {
          setControlOption(option);
          onUpdate(roomId, { control: option });
        }
        break;
      default:
        onUpdate(roomId, { [systemType]: option });
        break;
    }
  };



  const renderSystemCard = (systemType: string) => {
    let isEnabled = false;
    let options: Array<{ value: string; label: string; badge?: string }> = [];
    let selectedOption = '';
    let handleToggle = (enabled: boolean) => {};
    let handleOptionChange = (value: string) => {};

    switch (systemType) {
      case 'lighting':
        isEnabled = isLightingEnabled;
        handleToggle = (enabled: boolean) => {
          setIsLightingEnabled(enabled);
          handleSystemToggle('lighting', enabled);
        };
        break;
      case 'shades':
        isEnabled = shadesOption !== 'none';
        options = [
          { value: 'none', label: 'None' },
          { value: 'manual', label: 'Manual' },
          { value: 'motorized', label: 'Motorized', badge: 'recommended' },
        ];
        selectedOption = shadesOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('shades', value);
        break;
      case 'audio':
        isEnabled = audioOption !== 'none';
        options = [
          { value: 'none', label: 'None' },
          { value: 'hidden', label: 'Hidden Speakers' },
          { value: 'in-ceiling', label: 'In-Ceiling Speakers', badge: 'recommended' },
        ];
        selectedOption = audioOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('audio', value);
        break;
      case 'video':
        isEnabled = videoOption !== 'none';
        options = [
          { value: 'none', label: 'None' },
          { value: 'display', label: 'Display/TV' },
          { value: 'projector', label: 'Projector', badge: 'recommended' },
        ];
        selectedOption = videoOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('video', value);
        break;
      case 'network':
        isEnabled = isNetworkEnabled;
        handleToggle = (enabled: boolean) => {
          setIsNetworkEnabled(enabled);
          handleSystemToggle('network', enabled);
        };
        break;
      case 'security':
        isEnabled = isSecurityEnabled;
        handleToggle = (enabled: boolean) => {
          setIsSecurityEnabled(enabled);
          handleSystemToggle('security', enabled);
        };
        break;
      case 'control':
        isEnabled = controlOption !== 'none';
        options = [
          { value: 'none', label: 'None' },
          { value: 'wall keypad', label: 'Wall Keypad' },
          { value: 'remote', label: 'Remote' },
          { value: 'app', label: 'Mobile App', badge: 'recommended' },
        ];
        selectedOption = controlOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('control', value);
        break;
      default:
        return null;
    }

    const icon = getCategoryIcon(systemType);
    const label = getCategoryLabel(systemType);

    return (
      <Card className={`overflow-hidden transition-all duration-300 ${
        isEnabled ? 'border-primary/30 shadow-md bg-gradient-to-r from-card to-background/80' : 'hover:border-primary/20 hover:shadow-sm'
      }`} key={systemType}>
        <CardContent className="p-0">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${isEnabled ? 'bg-primary/10 text-primary' : 'text-muted-foreground'}`}>
                {icon}
              </div>
              <h3 className="text-lg font-medium">{label}</h3>
            </div>
            {(systemType === 'lighting' || systemType === 'network' || systemType === 'security') ? (
              <div className="inline-flex h-7 w-14 cursor-pointer items-center rounded-full bg-muted p-1 shadow-inner transition-all duration-300">
                <div
                  className={`flex h-6 w-6 items-center justify-center rounded-full transition-transform duration-300 ${
                    isEnabled ? 'translate-x-7 bg-primary text-primary-foreground' : 'bg-muted-foreground/20'
                  }`}
                  onClick={() => handleToggle(!isEnabled)}
                >
                  {isEnabled && <span className="flex h-2 w-2 rounded-full bg-primary-foreground"></span>}
                </div>
              </div>
            ) : null}
          </div>

          {options.length > 0 && (
            <div className="p-4 space-y-3">
              {options.map((option) => (
                <div className="flex items-center space-x-2" key={option.value}>
                  <Checkbox
                    id={`${systemType}-${option.value}`}
                    checked={selectedOption === option.value}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        handleOptionChange(option.value);
                      }
                    }}
                  />
                  <label
                    htmlFor={`${systemType}-${option.value}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                  >
                    {option.label}
                    {option.badge && (
                      <Badge
                        variant="outline"
                        className="ml-2 text-xs"
                      >
                        {option.badge === 'recommended'
                          ? 'Recommended'
                          : option.badge}
                      </Badge>
                    )}
                  </label>
                </div>
              ))}
            </div>
          )}

          {isEnabled && (systemType === 'network' || systemType === 'security') && (
            <div className="p-4 pt-0">
              <p className="text-sm text-muted-foreground">
                {systemType === 'network'
                  ? 'High-density networking configured for this room'
                  : 'Surveillance system configured for this room'
                }
              </p>
            </div>
          )}


        </CardContent>
      </Card>
    );
  };



  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {['lighting', 'shades', 'audio', 'video', 'network', 'security', 'control'].map(systemType =>
          renderSystemCard(systemType)
        )}
      </div>
    </div>
  );
};

export default SystemSelector;
