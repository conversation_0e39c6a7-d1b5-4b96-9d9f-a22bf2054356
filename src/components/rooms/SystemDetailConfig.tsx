
import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { RoomConfig } from '@/types/roomTypes';
import usePackageManagement from '@/hooks/usePackageManagement';
import { useCustomPackages } from '@/hooks/useCustomPackages';
import { Card, CardContent } from '@/components/ui/card';
import {
  Package,
  Image,
  LightbulbIcon,
  PanelTopIcon,
  SpeakerIcon,
  MonitorIcon,
  WifiIcon,
  ShieldIcon,
  MonitorSmartphone
} from 'lucide-react';

interface SystemDetailConfigProps {
  room: RoomConfig;
  updateRoom: (id: string, data: Partial<RoomConfig>) => void;
  // Change type to accept boolean values
  systemCategories: Record<string, boolean | string>;
}

// Helper functions for consistent styling
const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'lighting':
      return <LightbulbIcon className="h-5 w-5" />;
    case 'shades':
      return <PanelTopIcon className="h-5 w-5" />;
    case 'audio':
      return <SpeakerIcon className="h-5 w-5" />;
    case 'video':
      return <MonitorIcon className="h-5 w-5" />;
    case 'network':
      return <WifiIcon className="h-5 w-5" />;
    case 'security':
      return <ShieldIcon className="h-5 w-5" />;
    case 'control':
      return <MonitorSmartphone className="h-5 w-5" />;
    default:
      return <LightbulbIcon className="h-5 w-5" />;
  }
};

const getCategoryLabel = (category: string) => {
  switch (category) {
    case 'lighting':
      return 'Lighting';
    case 'shades':
      return 'Shades';
    case 'audio':
      return 'Audio';
    case 'video':
      return 'Video';
    case 'network':
      return 'Network';
    case 'security':
      return 'Security';
    case 'control':
      return 'Control Systems';
    default:
      return category.charAt(0).toUpperCase() + category.slice(1);
  }
};

const SystemDetailConfig: React.FC<SystemDetailConfigProps> = ({
  room,
  updateRoom,
  systemCategories
}) => {
  // Get packages from the useCustomPackages hook and system categories from usePackageManagement
  const { packages } = useCustomPackages();
  const { systemCategories: systemCategoriesMap } = usePackageManagement();

  // Helper function to render package item with icon and manufacturer info
  const renderPackageItem = (pkg: any) => {
    // Get the first product item to show representative info
    const firstItem = pkg.items && pkg.items.length > 0 ? pkg.items[0] : null;
    const product = firstItem?.product;

    return (
      <div className="flex items-center space-x-3 py-2">
        {/* Product Icon/Image */}
        <div className="flex-shrink-0 w-8 h-8 rounded-md overflow-hidden bg-muted/20 border">
          {product?.imageUrl ? (
            <img
              src={product.imageUrl}
              alt={product.name || pkg.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
                (e.target as HTMLImageElement).nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`flex items-center justify-center w-full h-full ${product?.imageUrl ? 'hidden' : ''}`}>
            <Image className="w-4 h-4 text-muted-foreground" />
          </div>
        </div>

        {/* Package and Product Info */}
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">
            {pkg.name} {pkg.isDefault ? "(Default)" : ""}
          </div>
          {product && (product.manufacturer || product.model) && (
            <div className="text-xs text-muted-foreground truncate">
              {product.manufacturer && product.model
                ? `${product.manufacturer} - ${product.model}`
                : product.manufacturer || product.model
              }
              {pkg.items && pkg.items.length > 1 && (
                <span className="ml-1">
                  +{pkg.items.length - 1} more item{pkg.items.length > 2 ? 's' : ''}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Filter systems that are active for this room
  const getActiveSystems = () => {
    const activeSystems = [];

    if (room.lighting) activeSystems.push('lighting');
    if (room.shades !== 'none') activeSystems.push('shades');
    if (room.audio !== 'none') activeSystems.push('audio');
    if (room.video !== 'none') activeSystems.push('video');
    if (room.network === 'high density') activeSystems.push('network');
    if (room.surveillance) activeSystems.push('security');
    if (room.control !== 'none') activeSystems.push('controlSystems');

    return activeSystems;
  };

  const activeSystems = getActiveSystems();

  // Get packages for a specific system category
  const getPackagesForSystem = (systemType: string) => {
    return packages.filter(pkg => pkg.systemCategory === systemType);
  };

  // Get default package for a system category (if any is marked as default)
  const getDefaultPackage = (systemType: string) => {
    const defaultPkg = packages.find(pkg => pkg.systemCategory === systemType && pkg.isDefault);
    return defaultPkg?.id || 'no-default';
  };

  // Get the currently selected package ID for a system
  const getSelectedPackage = (systemType: string) => {
    return room[`${systemType}PackageId`] || getDefaultPackage(systemType);
  };

  // Handle package selection
  const handleSelectPackage = (systemType: string, packageId: string) => {
    updateRoom(room.id, { [`${systemType}PackageId`]: packageId });
  };

  // Render configuration card for package selection
  const renderConfigurationCard = (systemType: string) => {
    const icon = getCategoryIcon(systemType);
    const label = getCategoryLabel(systemType);

    return (
      <Card className="overflow-hidden transition-all duration-300 border-primary/30 shadow-md bg-gradient-to-r from-card to-background/80" key={systemType}>
        <CardContent className="p-0">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-primary/10 text-primary">
                {icon}
              </div>
              <h3 className="text-lg font-medium">{label}</h3>
            </div>
          </div>

          {/* Package Selection Section */}
          <div className="p-4">
            <div className="space-y-3">
              <Label className="flex items-center gap-1.5 text-sm font-medium">
                <Package className="h-4 w-4" />
                <span>Package Selection</span>
              </Label>

              <Select
                value={getSelectedPackage(systemType)}
                onValueChange={(value) => handleSelectPackage(systemType, value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={`Select a ${label} package`} />
                </SelectTrigger>
                <SelectContent>
                  {getPackagesForSystem(systemType).map(pkg => (
                    <SelectItem key={pkg.id} value={pkg.id}>
                      {renderPackageItem(pkg)}
                    </SelectItem>
                  ))}
                  {getPackagesForSystem(systemType).length === 0 && (
                    <SelectItem value="no-packages" disabled>
                      <div className="flex items-center space-x-3 py-2">
                        <div className="flex-shrink-0 w-6 h-6 rounded-md overflow-hidden bg-muted/20 border">
                          <div className="flex items-center justify-center w-full h-full">
                            <Package className="w-3 h-3 text-muted-foreground" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm text-muted-foreground">
                            No packages available
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (activeSystems.length === 0) {
    return (
      <div className="py-8 text-center text-muted-foreground italic">
        <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No systems selected for this room yet</p>
        <p className="text-sm mt-2">Configure systems in the Systems tab first</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {activeSystems.map(systemType =>
        renderConfigurationCard(systemType)
      )}
    </div>
  );
};

export default SystemDetailConfig;
