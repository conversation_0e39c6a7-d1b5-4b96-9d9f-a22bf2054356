import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RoomConfig } from '@/types/roomTypes';
import { Trash2, Settings2, LayoutGrid, ClipboardEdit, Home } from 'lucide-react';
import { standardRooms } from './StandardRooms';
import SystemSelector from './SystemSelector';
import SystemDetailConfig from './SystemDetailConfig';

interface RoomCardProps {
  room: RoomConfig;
  systemCategories: {
    lighting: boolean;
    shades: boolean;
    audio: boolean;
    video: boolean;
    network: boolean;
    security: boolean;
    controlSystems: boolean;
  };
  updateRoom: (id: string, data: Partial<RoomConfig>) => void;
  removeRoom: (id: string) => void;
  errors: Record<string, string>;
}

const RoomCard: React.FC<RoomCardProps> = ({
  room,
  systemCategories,
  updateRoom,
  removeRoom,
  errors,
}) => {
  const [isCustom, setIsCustom] = useState<boolean>(
    !standardRooms.some(r => r.value === room.name)
  );
  const [activeTab, setActiveTab] = useState<string>("systems");

  const handleRoomTypeChange = (value: string) => {
    if (value === "Custom") {
      setIsCustom(true);
      // Don't update the name yet, let the user type it
    } else {
      setIsCustom(false);
      updateRoom(room.id, { name: value });
    }
  };

  // Generate options for floors (basement, 1st floor, 2nd floor, etc.)
  const floorOptions = [
    { value: "basement", label: "Basement" },
    { value: "1", label: "1st Floor" },
    { value: "2", label: "2nd Floor" },
    { value: "3", label: "3rd Floor" },
    { value: "4", label: "4th Floor" },
    { value: "5", label: "5th Floor" },
    { value: "other", label: "Other" }
  ];

  return (
    <AccordionItem
      value={room.id}
      className="border rounded-lg overflow-hidden"
    >
      <AccordionTrigger className="px-6 py-4 hover:no-underline">
        <div className="flex-1 text-left flex items-center gap-2">
          <span>{room.name || "Untitled Room"}</span>
          {room.floor && (
            <span className="text-xs bg-muted px-2 py-0.5 rounded">
              {typeof room.floor === 'string' && room.floor === 'basement' 
                ? 'Basement' 
                : typeof room.floor === 'string' && room.floor === 'other'
                ? 'Other'
                : `Floor ${room.floor}`}
            </span>
          )}
        </div>
      </AccordionTrigger>
      <AccordionContent className="px-6 pb-4">
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor={`room-type-${room.id}`}>
              Room Type <span className="text-red-500">*</span>
            </Label>
            <Select 
              value={isCustom ? "Custom" : room.name}
              onValueChange={handleRoomTypeChange}
            >
              <SelectTrigger id={`room-type-${room.id}`}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {standardRooms.map((roomType) => (
                  <SelectItem key={roomType.value} value={roomType.value}>
                    {roomType.label}
                  </SelectItem>
                ))}
                <SelectItem value="Custom">Custom</SelectItem>
              </SelectContent>
            </Select>
            
            {isCustom && (
              <div className="pt-2">
                <Label htmlFor={`room-name-${room.id}`}>
                  Custom Room Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id={`room-name-${room.id}`}
                  value={room.name}
                  onChange={(e) => updateRoom(room.id, { name: e.target.value })}
                  placeholder="Enter custom room name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>
            )}
          </div>

          {/* Floor selector */}
          <div className="space-y-2">
            <Label htmlFor={`room-floor-${room.id}`} className="flex items-center gap-1">
              <Home className="h-4 w-4" />
              Floor
            </Label>
            <Select 
              value={room.floor?.toString() || "1"}
              onValueChange={(value) => updateRoom(room.id, { floor: value })}
            >
              <SelectTrigger id={`room-floor-${room.id}`}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {floorOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="systems" className="flex items-center gap-1">
                <LayoutGrid className="h-4 w-4" />
                <span>Systems</span>
              </TabsTrigger>
              <TabsTrigger value="details" className="flex items-center gap-1">
                <Settings2 className="h-4 w-4" />
                <span>Configuration</span>
              </TabsTrigger>
              <TabsTrigger value="notes" className="flex items-center gap-1">
                <ClipboardEdit className="h-4 w-4" />
                <span>Notes</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="systems" className="mt-0">
              <SystemSelector
                roomSettings={room}
                roomId={room.id}
                onUpdate={updateRoom}
              />
            </TabsContent>
            
            <TabsContent value="details" className="mt-0">
              <SystemDetailConfig
                room={room}
                updateRoom={updateRoom}
                systemCategories={systemCategories}
              />
            </TabsContent>
            
            <TabsContent value="notes" className="mt-0">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor={`notes-${room.id}`}>Room Notes</Label>
                  <Textarea
                    id={`notes-${room.id}`}
                    value={room.notes}
                    onChange={(e) => updateRoom(room.id, { notes: e.target.value })}
                    placeholder="Additional notes about this room..."
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end">
            <Button
              variant="destructive"
              size="sm"
              onClick={() => removeRoom(room.id)}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Remove Room
            </Button>
          </div>
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};

export default RoomCard;
