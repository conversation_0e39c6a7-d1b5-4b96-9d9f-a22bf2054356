
import React, { useState } from 'react';
import { useFormContext } from '../../context/FormContext';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import FormNavigation from '../FormNavigation';

const QualificationStep: React.FC = () => {
  const { formData, setFormData } = useFormContext();
  const { qualificationInfo } = formData;
  const { toast } = useToast();
  
  const [errors, setErrors] = useState<Record<string, string>>({});

  const formatBudget = (value: number): string => {
    if (value >= 500000) return "$500K+";
    if (value >= 1000) return `$${Math.floor(value / 1000)}K`;
    return `$${value}`;
  };

  const updateQualificationInfo = <T extends keyof typeof qualificationInfo>(
    key: T,
    value: typeof qualificationInfo[T]
  ) => {
    setFormData((prev) => ({
      ...prev,
      qualificationInfo: {
        ...prev.qualificationInfo,
        [key]: value,
      },
    }));

    // Clear error for this field if it exists
    if (errors[key]) {
      setErrors((prev) => {
        const updated = { ...prev };
        delete updated[key];
        return updated;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!qualificationInfo.projectStage) {
      newErrors.projectStage = "Project stage is required";
    }

    if (!qualificationInfo.completionDate) {
      newErrors.completionDate = "Target completion date is required";
    }

    if (qualificationInfo.squareFootage <= 0) {
      newErrors.squareFootage = "Valid square footage is required";
    }

    if (qualificationInfo.floors <= 0) {
      newErrors.floors = "At least one floor is required";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      toast({
        title: "Please fix the errors",
        description: "Some required fields need your attention",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleNext = () => {
    return validateForm();
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-6">Project Qualification</h2>

      <div className="space-y-6">
        <div className="space-y-4">
          <Label>
            Budget Range: <span className="font-bold">{formatBudget(qualificationInfo?.budget || 50000)}</span>
          </Label>
          <Slider
            value={[qualificationInfo?.budget || 50000]}
            min={10000}
            max={500000}
            step={10000}
            onValueChange={(value) => updateQualificationInfo("budget", value[0])}
            className="py-4"
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>$10K</span>
            <span>$500K+</span>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="projectStage">Project Stage <span className="text-red-500">*</span></Label>
          <Select
            value={qualificationInfo?.projectStage || ""}
            onValueChange={(value) => updateQualificationInfo("projectStage", value)}
          >
            <SelectTrigger className={errors.projectStage ? "border-red-500" : ""}>
              <SelectValue placeholder="Select project stage" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Architectural">Architectural</SelectItem>
              <SelectItem value="Prewire">Prewire</SelectItem>
              <SelectItem value="Trim">Trim</SelectItem>
              <SelectItem value="Final Finish">Final Finish</SelectItem>
              <SelectItem value="Move-In Ready">Move-In Ready</SelectItem>
            </SelectContent>
          </Select>
          {errors.projectStage && <p className="text-sm text-red-500">{errors.projectStage}</p>}
        </div>

        <div className="space-y-2">
          <Label htmlFor="completionDate">Target Completion Date <span className="text-red-500">*</span></Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !qualificationInfo?.completionDate && "text-muted-foreground",
                  errors.completionDate && "border-red-500"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {qualificationInfo?.completionDate ? (
                  typeof qualificationInfo.completionDate === 'string' 
                    ? qualificationInfo.completionDate
                    : format(qualificationInfo.completionDate, "PPP")
                ) : (
                  <span>Select a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={typeof qualificationInfo?.completionDate === 'string' 
                  ? new Date(qualificationInfo.completionDate) 
                  : qualificationInfo?.completionDate as Date}
                onSelect={(date) => updateQualificationInfo("completionDate", date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          {errors.completionDate && <p className="text-sm text-red-500">{errors.completionDate}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="squareFootage">
              Approximate Square Footage <span className="text-red-500">*</span>
            </Label>
            <Input
              id="squareFootage"
              type="number"
              min={0}
              value={qualificationInfo?.squareFootage || ""}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 0;
                updateQualificationInfo("squareFootage", value);
              }}
              placeholder="Enter square footage"
              className={errors.squareFootage ? "border-red-500" : ""}
            />
            {errors.squareFootage && <p className="text-sm text-red-500">{errors.squareFootage}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="floors">Number of Floors <span className="text-red-500">*</span></Label>
            <Input
              id="floors"
              type="number"
              min={1}
              value={qualificationInfo?.floors || ""}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 0;
                updateQualificationInfo("floors", value);
              }}
              placeholder="Enter number of floors"
              className={errors.floors ? "border-red-500" : ""}
            />
            {errors.floors && <p className="text-sm text-red-500">{errors.floors}</p>}
          </div>
        </div>

        {qualificationInfo?.generatedScopeOfWork && (
          <div className="space-y-2 mt-4 p-4 border rounded-md bg-muted/30">
            <Label>Generated Scope of Work</Label>
            <div className="max-h-[200px] overflow-y-auto p-3 bg-background rounded border text-sm">
              {qualificationInfo.generatedScopeOfWork.split('\n').map((line, index) => (
                <p key={index} className={line.trim().endsWith(':') ? 'font-medium mt-2' : 'mt-1'}>
                  {line}
                </p>
              ))}
            </div>
          </div>
        )}
      </div>

      <FormNavigation onNext={handleNext} />
    </div>
  );
};

export default QualificationStep;
