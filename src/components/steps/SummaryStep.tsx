
import React, { useEffect } from 'react';
import { useFormContext } from '../../context/FormContext';
import FormNavigation from '../FormNavigation';
import { Button } from '@/components/ui/button';
import { CircleArrowUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import ClientInfoCard from './summary/ClientInfoCard';
import QualificationCard from './summary/QualificationCard';
import SystemCategoriesCard from './summary/SystemCategoriesCard';
import RoomsCard from './summary/RoomsCard';
import ExecutiveSummaryCard from './summary/ExecutiveSummaryCard';

const SummaryStep: React.FC = () => {
  const { formData, setFormData, goToStep } = useFormContext();
  const { clientInfo, qualificationInfo, systemCategories, rooms, projectFiles, executiveSummary } = formData;
  const { toast } = useToast();
  const finalActionsStep = 7; // Explicitly set to 7 for FinalActionsStep

  // Log when the component mounts
  useEffect(() => {
    console.log("SummaryStep mounted, final actions step index (hardcoded):", finalActionsStep);
    
    // Ensure all system package selections are included in the formData
    setFormData(prev => {
      // Simply touch the formData to ensure all latest changes are included
      return { ...prev };
    });
  }, [finalActionsStep, setFormData]);

  const handleUpdateExecutiveSummary = (summary: string) => {
    setFormData((prev) => ({
      ...prev,
      executiveSummary: summary,
    }));
  };
  
  // Consistent navigation function for both buttons to use
  const navigateToFinalActions = () => {
    console.log("SummaryStep: navigateToFinalActions called, going to step:", finalActionsStep);
    
    // Show a toast to indicate we're navigating
    toast({
      title: "Navigating to Final Actions",
      description: "Processing your summary and moving to final actions...",
    });
    
    // Directly navigate to the final actions step using goToStep
    goToStep(finalActionsStep);
    
    // Show confirmation toast after a brief delay
    setTimeout(() => {
      toast({
        title: "Summary Exported",
        description: "Project summary has been exported to the final actions page",
        variant: "default",
      });
    }, 500);
    
    return true; // Allow navigation to proceed
  };

  // Function to proceed to next step - uses the shared navigation function
  const handleNextStep = () => {
    console.log("SummaryStep: handleNextStep called");
    return navigateToFinalActions();
  };

  // Function to explicitly navigate to final actions - uses the same shared function
  const handleExportToFinalActions = () => {
    console.log("SummaryStep: handleExportToFinalActions called");
    navigateToFinalActions();
  };

  return (
    <div className="animate-fade-in space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Project Summary</h2>
        <p className="text-muted-foreground mb-6">
          Review and finalize the project details before generating reports and proposals.
        </p>
      </div>

      {/* Client Information Card */}
      <ClientInfoCard 
        clientInfo={clientInfo} 
        onEdit={() => goToStep(1)} 
      />

      {/* Project Qualification Card */}
      <QualificationCard 
        qualificationInfo={qualificationInfo} 
        onEdit={() => goToStep(2)} 
      />

      {/* Systems Selection Card */}
      <SystemCategoriesCard 
        systemCategories={systemCategories} 
        onEdit={() => goToStep(3)} 
      />

      {/* Rooms Card - Fixed props */}
      <RoomsCard onEditRoom={() => goToStep(4)} />

      {/* Executive Summary Card */}
      <ExecutiveSummaryCard 
        executiveSummary={executiveSummary || ""} 
        onUpdate={handleUpdateExecutiveSummary} 
      />

      {/* Export Button - Enhanced for better visibility and reliability */}
      <div className="flex justify-center mb-6">
        <Button 
          onClick={handleExportToFinalActions}
          size="lg" 
          className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 px-6 py-6 text-base"
        >
          Export to Final Actions
          <CircleArrowUp className="h-5 w-5" />
        </Button>
      </div>

      <FormNavigation 
        nextLabel="Continue To Final Actions" 
        onNext={handleNextStep}
      />
    </div>
  );
};

export default SummaryStep;
