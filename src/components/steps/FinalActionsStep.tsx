
import React, { useState } from 'react';
import { useFormContext } from '../../context/FormContext';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Download, SendHorizontal, FilePlus2, Calculator, ArrowUpFromLine } from 'lucide-react';
import { generateProjectPDF } from '@/utils/pdfGenerator';
import FormNavigation from '../FormNavigation';
import { useNavigate } from 'react-router-dom';
import ZapierActionButton from '../integrations/ZapierActionButton';
import ZohoCRMIntegration from '../integrations/ZohoCRMIntegration';
import { saveWalkthrough, updateWalkthrough } from '@/services/walkthroughService';

interface FinalActionsStepProps {
  onWalkthroughFinish?: () => void;
  currentWalkthroughId?: string | null;
  onWalkthroughSaved?: (id: string) => void;
}

const FinalActionsStep: React.FC<FinalActionsStepProps> = ({
  onWalkthroughFinish,
  currentWalkthroughId,
  onWalkthroughSaved
}) => {
  const { formData } = useFormContext();
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  const navigate = useNavigate();

  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      await generateProjectPDF(formData);
      toast({
        title: "PDF Generated",
        description: "Project summary has been exported as PDF"
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Export Failed",
        description: "Something went wrong while generating the PDF",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleCreateEstimate = () => {
    // Navigate to cost manager with walkthrough data
    navigate('/cost-manager', {
      state: {
        walkthroughData: formData
      }
    });

    toast({
      title: "Creating Estimate",
      description: "Transferring project data to Cost Manager"
    });
  };

  const handleDataSync = () => {
    // Navigate to actions with walkthrough data
    navigate('/actions', {
      state: {
        walkthroughData: formData
      }
    });

    toast({
      title: "Data Sync",
      description: "Opening data sync options"
    });
  };

  const handleFinish = async () => {
    console.log("FinalActionsStep: handleFinish called", { currentWalkthroughId });

    try {
      // Auto-save the walkthrough before finishing
      const title = `${formData.clientInfo.fullName || 'Unnamed Client'}'s Project`;
      let savedId: string | null = null;
      let isUpdate = false;

      if (currentWalkthroughId) {
        // Update existing walkthrough
        console.log("FinalActionsStep: Updating existing walkthrough with ID:", currentWalkthroughId);
        const success = await updateWalkthrough(currentWalkthroughId, title, formData);
        if (success) {
          savedId = currentWalkthroughId;
          isUpdate = true;
        }
      } else {
        // Create new walkthrough
        console.log("FinalActionsStep: Creating new walkthrough with title:", title);
        savedId = await saveWalkthrough(title, formData);
      }

      if (savedId) {
        console.log("FinalActionsStep: Walkthrough auto-saved successfully with ID:", savedId);

        // Notify parent component about the saved walkthrough ID
        if (onWalkthroughSaved && !isUpdate) {
          onWalkthroughSaved(savedId);
        }

        toast({
          title: "Walkthrough Completed & Saved",
          description: `${formData.clientInfo.fullName || 'Your'} walkthrough has been completed and ${isUpdate ? 'updated' : 'saved'} successfully.`
        });
      } else {
        console.error("FinalActionsStep: Failed to auto-save walkthrough");
        toast({
          title: "Walkthrough Completed",
          description: "Your walkthrough has been completed, but there was an issue saving it. Please use 'Save Progress' if needed.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("FinalActionsStep: Error auto-saving walkthrough:", error);
      toast({
        title: "Walkthrough Completed",
        description: "Your walkthrough has been completed, but there was an issue saving it. Please use 'Save Progress' if needed.",
        variant: "destructive"
      });
    }

    // Always call the finish callback regardless of save status
    if (onWalkthroughFinish) {
      console.log("FinalActionsStep: calling provided onWalkthroughFinish callback");
      onWalkthroughFinish();
    }
  };

  return (
    <div className="animate-fade-in space-y-6">
      <h2 className="text-2xl font-bold mb-2">Final Actions</h2>
      <p className="text-muted-foreground mb-6">
        Your walkthrough is complete! You can now perform any of the following actions with your project data.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border rounded-lg p-5 flex flex-col">
          <h3 className="font-semibold text-lg mb-2">Export & Documentation</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Export your walkthrough data or create project documentation.
          </p>
          <div className="mt-auto space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleExportPDF}
              disabled={isExporting}
            >
              <Download className="mr-2 h-4 w-4" />
              {isExporting ? 'Generating PDF...' : 'Export as PDF'}
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleCreateEstimate}
            >
              <Calculator className="mr-2 h-4 w-4" />
              Create Cost Estimate
            </Button>
          </div>
        </div>

        <div className="border rounded-lg p-5 flex flex-col">
          <h3 className="font-semibold text-lg mb-2">Data Integration</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Sync your walkthrough data to external systems.
          </p>
          <div className="mt-auto space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleDataSync}
            >
              <ArrowUpFromLine className="mr-2 h-4 w-4" />
              Sync Customer Data
            </Button>
          </div>
        </div>
      </div>

      <FormNavigation
        onFinish={handleFinish}
        showBackToSummary={true}
        nextLabel="Finish Walkthrough"
      />
    </div>
  );
};

export default FinalActionsStep;
