
import React, { useState, useRef, useEffect } from 'react';
import { useFormContext } from '../../context/FormContext';
import FormNavigation from '../FormNavigation';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Upload, FileX, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Define supported file types and their categories
const SUPPORTED_FILE_TYPES = {
  // Documents
  'application/pdf': { category: 'general', name: 'PDF' },
  'application/msword': { category: 'general', name: 'Word Document' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { category: 'general', name: 'Word Document' },
  'text/plain': { category: 'general', name: 'Text File' },
  'text/csv': { category: 'general', name: 'CSV File' },

  // Images (for floorplans and references)
  'image/jpeg': { category: 'floorplans', name: 'JPEG Image' },
  'image/jpg': { category: 'floorplans', name: 'JPG Image' },
  'image/png': { category: 'floorplans', name: 'PNG Image' },
  'image/gif': { category: 'references', name: 'GIF Image' },
  'image/bmp': { category: 'references', name: 'BMP Image' },
  'image/webp': { category: 'references', name: 'WebP Image' },

  // CAD and Design files
  'application/dwg': { category: 'floorplans', name: 'AutoCAD Drawing' },
  'application/dxf': { category: 'floorplans', name: 'AutoCAD Exchange' },

  // Spreadsheets
  'application/vnd.ms-excel': { category: 'general', name: 'Excel Spreadsheet' },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { category: 'general', name: 'Excel Spreadsheet' },
} as const;

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB limit

const FilesUploadStep: React.FC = () => {
  const { formData, setFormData } = useFormContext();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Sync local uploadedFiles state with formData.projectFiles when component mounts or formData changes
  useEffect(() => {
    if (formData.projectFiles) {
      const allFiles = [
        ...(formData.projectFiles.floorplans || []),
        ...(formData.projectFiles.references || []),
        ...(formData.projectFiles.general || [])
      ];
      setUploadedFiles(allFiles);
    }
  }, [formData.projectFiles]);

  const validateFile = (file: File): { isValid: boolean; error?: string; category?: string } => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File "${file.name}" is too large. Maximum size is 10MB.`
      };
    }

    // Check file type
    const fileType = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];
    if (!fileType) {
      const supportedTypes = Object.values(SUPPORTED_FILE_TYPES).map(type => type.name).join(', ');
      return {
        isValid: false,
        error: `File type "${file.type || 'unknown'}" is not supported. Supported types: ${supportedTypes}`
      };
    }

    return {
      isValid: true,
      category: fileType.category
    };
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploadError(null); // Clear any previous errors

      const files = e.target.files;
      if (!files?.length) return;

      const newFiles = Array.from(files);
      const validFiles: File[] = [];
      const errors: string[] = [];

      // Validate each file
      for (const file of newFiles) {
        const validation = validateFile(file);
        if (validation.isValid) {
          validFiles.push(file);
        } else {
          errors.push(validation.error!);
        }
      }

      // Show errors if any
      if (errors.length > 0) {
        const errorMessage = errors.join('\n');
        setUploadError(errorMessage);
        toast({
          title: 'File Upload Error',
          description: errors[0], // Show first error in toast
          variant: 'destructive'
        });
      }

      // Add valid files if any
      if (validFiles.length > 0) {
        setUploadedFiles(prev => [...prev, ...validFiles]);

        // Add files to formData with proper structure
        setFormData(prev => {
          const currentProjectFiles = prev.projectFiles || {
            floorplans: [],
            references: [],
            general: []
          };

          // Categorize files
          const newFloorplans: File[] = [];
          const newReferences: File[] = [];
          const newGeneral: File[] = [];

          validFiles.forEach(file => {
            const fileType = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];
            if (fileType) {
              switch (fileType.category) {
                case 'floorplans':
                  newFloorplans.push(file);
                  break;
                case 'references':
                  newReferences.push(file);
                  break;
                default:
                  newGeneral.push(file);
              }
            }
          });

          return {
            ...prev,
            projectFiles: {
              floorplans: [...currentProjectFiles.floorplans, ...newFloorplans],
              references: [...currentProjectFiles.references, ...newReferences],
              general: [...(currentProjectFiles.general || []), ...newGeneral]
            }
          };
        });

        // Show success message
        toast({
          title: 'Files Uploaded',
          description: `Successfully uploaded ${validFiles.length} file${validFiles.length > 1 ? 's' : ''}`,
          variant: 'default'
        });
      }

      // Clear the input value so the same file can be uploaded again if needed
      e.target.value = '';
    } catch (error) {
      console.error('Error in handleFileChange:', error);
      const errorMessage = 'An unexpected error occurred while uploading files.';
      setUploadError(errorMessage);
      toast({
        title: 'Upload Error',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removeFile = (index: number) => {
    const fileToRemove = uploadedFiles[index];
    if (!fileToRemove) return;

    // Remove from local state
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));

    // Remove from formData - need to find which category the file is in
    setFormData(prev => {
      const currentProjectFiles = prev.projectFiles || {
        floorplans: [],
        references: [],
        general: []
      };

      // Find and remove the file from the appropriate category
      const fileType = SUPPORTED_FILE_TYPES[fileToRemove.type as keyof typeof SUPPORTED_FILE_TYPES];
      if (fileType) {
        const updatedProjectFiles = { ...currentProjectFiles };

        switch (fileType.category) {
          case 'floorplans':
            updatedProjectFiles.floorplans = updatedProjectFiles.floorplans.filter(f => f !== fileToRemove);
            break;
          case 'references':
            updatedProjectFiles.references = updatedProjectFiles.references.filter(f => f !== fileToRemove);
            break;
          default:
            updatedProjectFiles.general = (updatedProjectFiles.general || []).filter(f => f !== fileToRemove);
        }

        return {
          ...prev,
          projectFiles: updatedProjectFiles
        };
      }

      return prev;
    });

    toast({
      title: 'File Removed',
      description: `Removed "${fileToRemove.name}"`,
      variant: 'default'
    });
  };

  const getSupportedFileTypesText = () => {
    const types = Object.values(SUPPORTED_FILE_TYPES).map(type => type.name);
    return types.join(', ');
  };

  const getFileCategoryBadge = (file: File) => {
    const fileType = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];
    if (!fileType) return null;

    const categoryColors = {
      floorplans: 'bg-blue-100 text-blue-800',
      references: 'bg-green-100 text-green-800',
      general: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${categoryColors[fileType.category as keyof typeof categoryColors]}`}>
        {fileType.category}
      </span>
    );
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-6">Project Files</h2>
      <p className="text-muted-foreground mb-8">
        Upload any relevant project files, documents, or plans. Supported file types: {getSupportedFileTypesText()}
      </p>

      {uploadError && (
        <Alert className="mb-6" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="whitespace-pre-line">
            {uploadError}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-8">
        <div className="bg-muted/30 rounded-lg p-6">
          <div className="text-center space-y-4">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
              accept={Object.keys(SUPPORTED_FILE_TYPES).join(',')}
            />

            <Button
              variant="outline"
              onClick={triggerFileInput}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Choose Files
            </Button>

            <p className="text-sm text-muted-foreground">
              Upload documents, plans, or diagrams (Max 10MB per file)
            </p>
          </div>

          {uploadedFiles.length > 0 && (
            <div className="mt-6">
              <Label className="mb-2 block">Uploaded Files ({uploadedFiles.length})</Label>
              <div className="space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div key={index} className="flex justify-between items-center bg-background p-3 rounded border">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <span className="text-sm truncate">{file.name}</span>
                      {getFileCategoryBadge(file)}
                      <span className="text-xs text-muted-foreground">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="h-8 w-8 p-0 flex-shrink-0"
                    >
                      <FileX className="h-4 w-4" />
                      <span className="sr-only">Remove file</span>
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <FormNavigation />
    </div>
  );
};

export default FilesUploadStep;
