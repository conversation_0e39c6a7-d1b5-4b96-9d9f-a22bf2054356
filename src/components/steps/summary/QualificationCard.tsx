
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';
import { QualificationInfo } from '@/types/formTypes';

interface QualificationCardProps {
  qualificationInfo: QualificationInfo;
  onEdit: () => void;
}

const QualificationCard: React.FC<QualificationCardProps> = ({ qualificationInfo, onEdit }) => {
  // Helper function to format completion date safely
  const formatCompletionDate = () => {
    if (!qualificationInfo.completionDate) {
      return "Not specified";
    }
    
    try {
      // Handle both string and Date objects
      if (typeof qualificationInfo.completionDate === 'string') {
        return new Date(qualificationInfo.completionDate).toLocaleDateString();
      } else if (qualificationInfo.completionDate instanceof Date) {
        return qualificationInfo.completionDate.toLocaleDateString();
      }
      return "Invalid date";
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Date format error";
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between">
          <div>
            <CardTitle>Project Qualification</CardTitle>
            <CardDescription>Budget and timeline information</CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onEdit}
            className="text-xs flex items-center gap-1"
          >
            <Edit className="h-3 w-3" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <dt className="text-muted-foreground">Budget</dt>
            <dd className="font-medium">${qualificationInfo.budget?.toLocaleString() || "Not specified"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Project Stage</dt>
            <dd className="font-medium">{qualificationInfo.projectStage || "Not specified"}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Completion Date</dt>
            <dd className="font-medium">{formatCompletionDate()}</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Square Footage</dt>
            <dd className="font-medium">{qualificationInfo.squareFootage?.toLocaleString() || "Not specified"} sq ft</dd>
          </div>
          <div>
            <dt className="text-muted-foreground">Number of Floors</dt>
            <dd className="font-medium">{qualificationInfo.floors || "1"}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
};

export default QualificationCard;
