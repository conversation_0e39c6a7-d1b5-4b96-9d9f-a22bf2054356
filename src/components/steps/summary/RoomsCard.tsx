
import React from 'react';
import { useFormContext } from '@/context/FormContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Edit } from 'lucide-react';
import usePackageManagement from '@/hooks/usePackageManagement';

interface RoomsCardProps {
  onEditRoom: () => void;
}

const RoomsCard: React.FC<RoomsCardProps> = ({ onEditRoom }) => {
  const { formData } = useFormContext();
  const { rooms } = formData;
  const { items } = usePackageManagement();

  const getPackageName = (systemType: string, packageId?: string) => {
    if (!packageId) return 'No Package Selected';
    const pkg = items.find(p => p.id === packageId);
    return pkg ? pkg.name : 'Unknown Package';
  };

  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">Rooms Configuration</CardTitle>
        <Edit className="h-4 w-4 cursor-pointer" onClick={onEditRoom} />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {rooms.map((room) => (
            <Card key={room.id} className="shadow-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{room.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  {room.lighting && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Lighting</Badge>
                      <span>{getPackageName('lighting', room.lightingPackageId)}</span>
                    </div>
                  )}
                  {room.shades !== 'none' && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Shades</Badge>
                      <span>{getPackageName('shades', room.shadesPackageId)}</span>
                    </div>
                  )}
                  {room.audio !== 'none' && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Audio</Badge>
                      <span>{getPackageName('audio', room.audioPackageId)}</span>
                    </div>
                  )}
                  {room.video !== 'none' && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Video</Badge>
                      <span>{getPackageName('video', room.videoPackageId)}</span>
                    </div>
                  )}
                  {room.network === 'high density' && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Network</Badge>
                      <span>{getPackageName('network', room.networkPackageId)}</span>
                    </div>
                  )}
                  {room.surveillance && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Surveillance</Badge>
                      <span>{getPackageName('security', room.securityPackageId)}</span>
                    </div>
                  )}
                  {room.control !== 'none' && (
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Control</Badge>
                      <span>{getPackageName('controlSystems', room.controlSystemsPackageId)}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RoomsCard;
