
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit, Package, ChevronDown, ChevronUp } from 'lucide-react';
import { SystemCategories } from '@/types/formTypes';
import { WeQuotePackage } from '@/services/zapierService';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';

interface SystemCategoriesCardProps {
  systemCategories: SystemCategories;
  onEdit: () => void;
}

const SystemCategoriesCard: React.FC<SystemCategoriesCardProps> = ({ systemCategories, onEdit }) => {
  // Get all enabled systems
  const enabledSystems = Object.entries(systemCategories)
    .filter(([_, enabled]) => enabled)
    .map(([key]) => key);
  
  // State for packages data
  const [packages, setPackages] = useState<WeQuotePackage[]>([]);
  const [defaultPackages, setDefaultPackages] = useState<WeQuotePackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Initialize collapsed state for each system
  const [collapsedState, setCollapsedState] = useState<Record<string, boolean>>(
    enabledSystems.reduce((acc, system) => ({ ...acc, [system]: true }), {})
  );
  
  // Load packages data
  useEffect(() => {
    const loadPackages = async () => {
      try {
        // For now, we'll just use empty arrays until we have real data loading
        // This prevents the Promise.length errors
        setPackages([]);
        setDefaultPackages([]);
      } catch (error) {
        console.error('Error loading packages:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPackages();
  }, []);
  
  // Helper function to get packages for a specific system category
  const getPackagesBySystemCategory = (category: string): WeQuotePackage[] => {
    return packages.filter(pkg => pkg.systemCategory === category);
  };
  
  // Count packages for each system
  const packageCounts = enabledSystems.reduce<Record<string, number>>((acc, system) => {
    acc[system] = getPackagesBySystemCategory(system).length;
    return acc;
  }, {});
  
  // Toggle collapsed state for a system
  const toggleSystemCollapse = (system: string) => {
    setCollapsedState(prev => ({
      ...prev,
      [system]: !prev[system]
    }));
  };

  // Helper to convert system key to display name
  const getSystemDisplayName = (key: string) => {
    switch (key) {
      case 'lighting': return 'Lighting System';
      case 'shades': return 'Shading System';
      case 'audio': return 'Audio System';
      case 'video': return 'Video System';
      case 'network': return 'Networking System';
      case 'security': return 'Surveillance System';
      case 'controlSystems': return 'Control System';
      default: return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }
  };
  
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between">
          <div>
            <CardTitle>System Categories</CardTitle>
            <CardDescription>Selected technology systems</CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onEdit}
            className="text-xs flex items-center gap-1"
          >
            <Edit className="h-3 w-3" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {enabledSystems.length > 0 ? (
          <div className="space-y-3">
            {enabledSystems.map(system => {
              const isCollapsed = collapsedState[system] !== false;
              
              return (
                <Collapsible 
                  key={system} 
                  open={!isCollapsed}
                  onOpenChange={() => toggleSystemCollapse(system)}
                  className="border rounded-md overflow-hidden"
                >
                  <CollapsibleTrigger className="flex w-full justify-between items-center p-3 hover:bg-muted/50 text-left">
                    <div className="flex items-center">
                      <span className="font-medium">
                        {getSystemDisplayName(system)}
                      </span>
                      {packageCounts[system] > 0 && (
                        <Badge variant="secondary" className="ml-2 flex items-center">
                          <Package className="h-2.5 w-2.5 mr-0.5" />
                          {packageCounts[system]}
                        </Badge>
                      )}
                    </div>
                    {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <div className="p-3 pt-0 border-t text-sm space-y-2">
                      <p className="text-muted-foreground">
                        {system === 'lighting' && "Smart lighting controls and fixtures"}
                        {system === 'shades' && "Motorized window treatments"}
                        {system === 'audio' && "Multi-room audio systems"}
                        {system === 'video' && "Video displays and distribution"}
                        {system === 'network' && "Enterprise-grade networking"}
                        {system === 'security' && "Security cameras and monitoring"}
                        {system === 'controlSystems' && "Home automation control"}
                      </p>
                      
                      {packageCounts[system] > 0 && (
                        <div>
                          <div className="font-medium mb-1">Packages:</div>
                          <ul className="list-disc list-inside">
                            {getPackagesBySystemCategory(system).map(pkg => (
                              <li key={pkg.id} className="ml-2">
                                {pkg.name}
                                {pkg.isDefault && <span className="text-xs ml-2 text-muted-foreground">(Default)</span>}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              );
            })}
          </div>
        ) : (
          <span className="text-muted-foreground italic text-sm">No systems selected</span>
        )}
        
        {packages.length > 0 && (
          <div className="mt-4 space-y-1">
            <div className="text-sm flex items-center gap-1 text-muted-foreground">
              <Package className="h-3.5 w-3.5" />
              <span>Total packages: {packages.length}</span>
            </div>
            
            {defaultPackages.length > 0 && (
              <div className="text-sm flex items-center gap-1 text-muted-foreground">
                <Package className="h-3.5 w-3.5" />
                <span>Default packages: {defaultPackages.length}</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SystemCategoriesCard;
