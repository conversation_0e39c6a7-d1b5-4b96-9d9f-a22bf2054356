
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import AIAnalysisComponent from '../../integrations/AIAnalysisComponent';

interface ExecutiveSummaryCardProps {
  executiveSummary: string;
  onUpdate: (summary: string) => void;
}

const ExecutiveSummaryCard: React.FC<ExecutiveSummaryCardProps> = ({ executiveSummary, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempSummary, setTempSummary] = useState(executiveSummary || "");
  const { toast } = useToast();

  const handleSave = () => {
    onUpdate(tempSummary);
    setIsEditing(false);
    toast({
      title: "Summary Updated",
      description: "Executive summary has been updated",
    });
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between">
          <div>
            <CardTitle>Executive Summary</CardTitle>
            <CardDescription>Generated based on project details</CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => {
              setTempSummary(executiveSummary || "");
              setIsEditing(!isEditing);
            }}
            className="text-xs flex items-center gap-1"
          >
            <Edit className="h-3 w-3" />
            {isEditing ? "Cancel" : "Edit"}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <div className="space-y-4">
            <Textarea 
              value={tempSummary} 
              onChange={(e) => setTempSummary(e.target.value)}
              rows={10}
              placeholder="Enter executive summary here..."
              className="font-normal"
            />
            <div className="flex justify-end">
              <Button onClick={handleSave}>
                Save Summary
              </Button>
            </div>
          </div>
        ) : (
          <>
            {executiveSummary ? (
              <div className="prose prose-sm max-w-none">
                {executiveSummary.split('\n').map((paragraph, index) => (
                  paragraph ? <p key={index} className="mb-4">{paragraph}</p> : <br key={index} />
                ))}
              </div>
            ) : (
              <div>
                <p className="text-muted-foreground italic mb-4">No executive summary has been generated yet.</p>
                <AIAnalysisComponent analysisType="summary-report" />
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ExecutiveSummaryCard;
