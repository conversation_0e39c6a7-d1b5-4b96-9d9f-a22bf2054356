
import React, { useRef, useState } from 'react';
import { useFormContext } from '../../context/FormContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import FormNavigation from '../FormNavigation';
import { Accordion } from '@/components/ui/accordion';
import { Plus, Calculator } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import RoomCard from '../rooms/RoomCard';
import { standardRooms } from '../rooms/StandardRooms';
import AutomatedQualification from '../audio/AutomatedQualification';
import { useNavigate } from 'react-router-dom';

const RoomWalkthroughStep: React.FC = () => {
  const { formData, setFormData, addRoom, updateRoom, removeRoom } = useFormContext();
  const { rooms, systemCategories } = formData;
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const [errors, setErrors] = useState<Record<string, Record<string, string>>>({});
  // Track which room accordions are open (empty array means all closed by default)
  const [openRooms, setOpenRooms] = useState<string[]>([]);

  const validateForm = () => {
    const newErrors: Record<string, Record<string, string>> = {};
    let hasErrors = false;

    rooms.forEach((room) => {
      const roomErrors: Record<string, string> = {};

      if (!room.name.trim()) {
        roomErrors.name = "Room name is required";
        hasErrors = true;
      }

      if (Object.keys(roomErrors).length > 0) {
        newErrors[room.id] = roomErrors;
      }
    });

    setErrors(newErrors);

    if (hasErrors) {
      toast({
        title: "Please fix the errors",
        description: "Some rooms need your attention",
        variant: "destructive",
      });
      return false;
    }

    if (rooms.length === 0) {
      toast({
        title: "No rooms added",
        description: "Please add at least one room to continue",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleNext = () => {
    return validateForm();
  };

  const addNewRoom = (roomType?: string) => {
    // If no room type is specified, add a custom untitled room
    if (!roomType) {
      addRoom("Untitled Room");
      
      toast({
        title: "Room added",
        description: `Added custom room`
      });
      return;
    }
    
    // Otherwise use the selected room type
    // Add the room with the selected name from the dropdown
    addRoom(roomType);
    
    toast({
      title: "Room added",
      description: `Added ${roomType}`
    });
  };

  const handleCreateEstimate = () => {
    if (!validateForm()) {
      return;
    }
    
    // Navigate to cost manager with walkthrough data
    navigate('/cost-manager', { 
      state: { 
        walkthroughData: formData 
      }
    });
    
    toast({
      title: "Creating Estimate",
      description: "Transferring room data to Cost Manager"
    });
  };

  // Add a dropdown for adding predefined rooms
  const renderRoomSelector = () => {
    return (
      <div className="space-y-4">
        <Label>Add a standard room</Label>
        <div className="flex flex-wrap gap-2">
          <Select onValueChange={(value) => addNewRoom(value)}>
            <SelectTrigger className="w-full md:w-auto">
              <SelectValue placeholder="Select a room type" />
            </SelectTrigger>
            <SelectContent>
              {standardRooms.map((room) => (
                <SelectItem key={room.value} value={room.value}>{room.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            onClick={() => addNewRoom()}
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Custom Room
          </Button>
          
          {rooms.length > 0 && (
            <Button
              variant="outline"
              className="ml-auto"
              onClick={handleCreateEstimate}
            >
              <Calculator className="mr-2 h-4 w-4" />
              Create Estimate
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Handle accordion value change
  const handleAccordionChange = (values: string[]) => {
    setOpenRooms(values);
  };

  return (
    <div className="animate-fade-in space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-6">Room-by-Room Walkthrough</h2>
        <p className="text-muted-foreground mb-6">
          Add and configure rooms for your project. For each room, specify the systems, floor, and requirements.
        </p>

        <AutomatedQualification />
      </div>

      <div className="space-y-6">
        <h3 className="text-xl font-semibold">Room Configuration</h3>
        
        {renderRoomSelector()}

        <div className="space-y-6 mt-6">
          {rooms.length === 0 ? (
            <div className="text-center py-12 border border-dashed rounded-lg">
              <p className="text-muted-foreground mb-4">No rooms added yet</p>
              <p className="text-muted-foreground mb-4">Select a room type from above to begin or use the automated qualification to detect rooms</p>
            </div>
          ) : (
            <Accordion
              type="multiple"
              value={openRooms}
              onValueChange={handleAccordionChange}
              className="space-y-4"
            >
              {rooms.map((room) => (
                <RoomCard
                  key={room.id}
                  room={room}
                  systemCategories={systemCategories}
                  updateRoom={updateRoom}
                  removeRoom={removeRoom}
                  errors={errors[room.id] || {}}
                />
              ))}
            </Accordion>
          )}
        </div>
      </div>

      <FormNavigation onNext={handleNext} />
    </div>
  );
};

export default RoomWalkthroughStep;
