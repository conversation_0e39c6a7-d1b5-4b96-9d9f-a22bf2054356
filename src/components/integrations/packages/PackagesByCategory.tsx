import React from 'react';
import { WeQuotePackage } from '@/services/zapierService';
import { systemCategories } from '@/hooks/usePackageManagement';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import PackageCard from './PackageCard';

interface PackagesByCategoryProps {
  categories: string[];
  packages: WeQuotePackage[];
  onAddPackage: (category: string) => void;
  onEditPackage: (pkg: WeQuotePackage) => void;
  onDeletePackage: (id: string) => void;
  onToggleDefault?: (id: string, systemCategory: string) => void;
  isStandalone?: boolean;
}

const PackagesByCategory: React.FC<PackagesByCategoryProps> = ({
  categories,
  packages,
  onAddPackage,
  onEditPackage,
  onDeletePackage,
  onToggleDefault,
  isStandalone = false
}) => {
  return (
    <div className="space-y-6">
      {categories.map(category => {
        const categoryPackages = packages.filter(pkg => pkg.systemCategory === category);
        if (categoryPackages.length === 0 && !isStandalone) return null;
        
        const categoryLabel = systemCategories[category as keyof typeof systemCategories] || 
                  category.charAt(0).toUpperCase() + category.slice(1).replace(/([A-Z])/g, ' $1');
        
        return (
          <Card key={category} className="overflow-hidden">
            <CardHeader className="bg-muted/30 py-3 px-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">
                  {categoryLabel}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAddPackage(category)}
                  className="shadow-sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Package
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="p-6">
              {categoryPackages.length === 0 ? (
                <p className="text-sm text-muted-foreground italic">
                  No packages defined for this system category
                </p>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {categoryPackages.map(pkg => (
                    <PackageCard
                      key={pkg.id}
                      pkg={pkg}
                      onEdit={onEditPackage}
                      onDelete={onDeletePackage}
                      onToggleDefault={onToggleDefault}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default PackagesByCategory;
