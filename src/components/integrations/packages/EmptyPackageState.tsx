
import React from 'react';
import { Button } from '@/components/ui/button';
import { Package, Upload, FileUp, Plus } from 'lucide-react';

interface EmptyPackageStateProps {
  onAddPackage?: () => void; // Made optional for backward compatibility
  onAddNew?: () => void; // Added this prop
  onImportTemplate?: () => void;
  onImportCSV?: () => void;
  onImport?: () => void; // Added this prop
}

const EmptyPackageState: React.FC<EmptyPackageStateProps> = ({
  onAddPackage,
  onAddNew,
  onImportTemplate,
  onImportCSV,
  onImport
}) => {
  // Use the most appropriate handler based on what's available
  const handleAddClick = onAddNew || onAddPackage || (() => {});
  const handleImportClick = onImport || onImportCSV || onImportTemplate || (() => {});

  return (
    <div className="border border-dashed rounded-lg p-8 flex flex-col items-center text-center">
      <Package className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-medium mb-2">No items yet</h3>
      <p className="text-sm text-muted-foreground mb-6">
        Create your first item or import existing ones to get started
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        <Button 
          variant="gradient"
          onClick={handleAddClick}
          className="shadow-sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create New Item
        </Button>
        
        <div className="flex gap-2">
          {onImportTemplate && (
            <Button
              variant="outline"
              onClick={onImportTemplate}
              className="shadow-sm"
            >
              <Upload className="h-4 w-4 mr-2" />
              Import Template
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={handleImportClick}
            className="shadow-sm"
          >
            <FileUp className="h-4 w-4 mr-2" />
            Import CSV
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EmptyPackageState;
