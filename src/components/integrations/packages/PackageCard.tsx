
import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit, Trash, Star, Package as PackageIcon, Info } from 'lucide-react';
import { WeQuotePackage } from '@/services/zapierService';
import { systemCategories } from '@/hooks/usePackageManagement'; // This import is now correct
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AspectRatio } from '@/components/ui/aspect-ratio';

interface PackageCardProps {
  pkg: WeQuotePackage;
  onEdit: (pkg: WeQuotePackage) => void;
  onDelete: (id: string) => void;
  onToggleDefault?: (id: string, systemCategory: string) => void;
}

const PackageCard: React.FC<PackageCardProps> = ({ 
  pkg, 
  onEdit, 
  onDelete,
  onToggleDefault
}) => {
  const totalItems = pkg.items?.length || 0;
  const totalCost = pkg.items?.reduce((sum, item) => sum + ((item.unitCost || 0) * (item.quantity || 1)), 0) || 0;
  
  // Format as currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };
  
  // Get readable category name
  const getCategoryName = () => {
    const categoryKey = pkg.systemCategory as keyof typeof systemCategories;
    return systemCategories[categoryKey] || 
      pkg.systemCategory.charAt(0).toUpperCase() + pkg.systemCategory.slice(1);
  };
  
  // Handle image error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = '/placeholder.svg';
  };

  // Add handlers for edit, delete and toggle default with proper event stopping
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onEdit(pkg);
  };
  
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onDelete(pkg.id);
  };
  
  const handleToggleDefault = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    if (onToggleDefault && pkg.systemCategory) {
      onToggleDefault(pkg.id, pkg.systemCategory);
    }
  };

  console.log("Package:", pkg.name, "Can toggle default:", !!onToggleDefault);

  return (
    <Card className={cn(
      "transition-all hover:shadow-md", 
      pkg.isDefault ? "border-2 border-amber-300" : ""
    )}>
      {pkg.imageUrl && (
        <div className="p-3 pb-0">
          <AspectRatio ratio={16/9} className="bg-muted rounded-md overflow-hidden">
            <img
              src={pkg.imageUrl}
              alt={pkg.name}
              className="object-cover w-full h-full"
              onError={handleImageError}
            />
          </AspectRatio>
        </div>
      )}
      <CardHeader className={pkg.imageUrl ? "pb-2 pt-3" : "pb-2"}>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{pkg.name}</CardTitle>
            <CardDescription className="flex items-center gap-1">
              {getCategoryName()}
              {pkg.categoryGroup && (
                <span className="text-xs bg-gray-100 px-1.5 py-0.5 rounded">
                  {pkg.categoryGroup}
                </span>
              )}
            </CardDescription>
          </div>
          {pkg.isDefault && (
            <div className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium">
              Default
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <p className="text-sm text-muted-foreground mb-2">{pkg.description || 'No description'}</p>
        
        {pkg.manufacturer && (
          <div className="text-xs text-muted-foreground mt-1 mb-3">
            <span className="font-medium">Manufacturer: </span>
            {pkg.manufacturer} {pkg.manufacturerSku && `(${pkg.manufacturerSku})`}
          </div>
        )}
        
        <div className="grid grid-cols-2 gap-2 mt-2">
          <div className="bg-gray-50 p-2 rounded">
            <p className="text-xs text-muted-foreground">Items</p>
            <p className="font-medium">{totalItems}</p>
          </div>
          <div className="bg-gray-50 p-2 rounded">
            <p className="text-xs text-muted-foreground">Total Cost</p>
            <p className="font-medium">{formatCurrency(totalCost)}</p>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-3">
        <div className="flex gap-2">
          <Button onClick={handleEdit} size="sm" variant="outline" className="shadow-sm">
            <Edit className="h-4 w-4" />
          </Button>
          {onToggleDefault && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    onClick={handleToggleDefault} 
                    size="sm" 
                    variant={pkg.isDefault ? "default" : "outline"}
                    className={cn(
                      "shadow-sm",
                      pkg.isDefault ? "bg-amber-500 hover:bg-amber-600" : ""
                    )}
                  >
                    <Star className={cn("h-4 w-4", pkg.isDefault ? "fill-white" : "")} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {pkg.isDefault ? "Remove default status" : "Set as default"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {pkg.longDescription && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="shadow-sm"
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  {pkg.longDescription}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <Button 
          onClick={handleDelete} 
          size="sm" 
          variant="ghost" 
          className="text-destructive"
        >
          <Trash className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PackageCard;
