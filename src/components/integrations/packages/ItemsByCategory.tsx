
import React, { useState } from 'react';
import { ProductItem } from '@/hooks/usePackageManagement'; // Import ProductItem directly
import { systemCategories } from '@/hooks/usePackageManagement'; // Fixed import
import ItemCard from './ItemCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Plus, MoreHorizontal, Edit, Trash2, Check, Square } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Checkbox } from '@/components/ui/checkbox';

// Update the WeQuotePackageItem interface to make the unit property required
export interface WeQuotePackageItem {
  id: string;
  name: string;
  description: string; // Changed from optional to required to match ProductItem
  model: string;       // Changed from optional to required to match ProductItem
  manufacturer: string; // Changed from optional to required to match ProductItem
  systemCategory: string;
  unitCost: number;
  quantity: number;
  unit: string;        // Changed from optional to required to match ProductItem
  subcategories?: string[];
  sku: string;
  imageUrl?: string;
  msrp?: number;
  tradePrice?: number;
  documentUrl?: string;
  documentName?: string;
  isDefault?: boolean;
}

interface ItemsByCategoryProps {
  categories: string[];
  items: WeQuotePackageItem[];
  onAddItem: (category: string) => void;
  onEditItem: (item: WeQuotePackageItem | string) => void;
  onDeleteItem: (item: WeQuotePackageItem | string) => void;
  isStandalone?: boolean;
  viewMode?: 'card' | 'list';
  selectedItems?: string[];
  onSelectItem?: (itemId: string, isSelected: boolean) => void;
  bulkEditMode?: boolean;
}

const ItemsByCategory: React.FC<ItemsByCategoryProps> = ({
  categories,
  items,
  onAddItem,
  onEditItem,
  onDeleteItem,
  isStandalone = false,
  viewMode = 'card',
  selectedItems = [],
  onSelectItem,
  bulkEditMode = false
}) => {
  // Group items by category
  const itemsByCategory = categories.reduce<Record<string, WeQuotePackageItem[]>>((acc, category) => {
    acc[category] = items.filter(item => item.systemCategory === category);
    return acc;
  }, {});

  // Render items in card view
  const renderCardView = (category: string, categoryItems: WeQuotePackageItem[]) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categoryItems.map(item => (
          <div key={item.id} className="relative">
            {bulkEditMode && onSelectItem && (
              <div className="absolute left-2 top-2 z-10">
                <Checkbox
                  checked={selectedItems.includes(item.id)}
                  onCheckedChange={(checked) => 
                    onSelectItem(item.id, checked === true)
                  }
                  className="bg-white/90 border-primary"
                />
              </div>
            )}
            <ItemCard
              key={item.id}
              item={item}
              onEdit={() => onEditItem(item)}
              onDelete={() => onDeleteItem(item)}
              isStandalone={isStandalone}
              selected={selectedItems.includes(item.id)}
            />
          </div>
        ))}
        {/* Add item card */}
        <Card className="border-dashed border-2 hover:border-primary/50 cursor-pointer h-[260px] flex flex-col justify-center items-center" onClick={() => onAddItem(category)}>
          <CardContent className="flex flex-col items-center justify-center h-full p-6 text-center">
            <div className="rounded-full bg-primary/10 p-4 mb-4">
              <Plus className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-lg font-medium">Add Item</h3>
            <p className="text-sm text-muted-foreground mt-2">Add a new item to {systemCategories[category]}</p>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Render items in list view
  const renderListView = (categoryItems: WeQuotePackageItem[]) => {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {bulkEditMode && onSelectItem && (
                <TableHead className="w-12">Select</TableHead>
              )}
              <TableHead>Name</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Qty</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categoryItems.map(item => (
              <TableRow key={item.id} className={selectedItems.includes(item.id) ? "bg-primary/5" : ""}>
                {bulkEditMode && onSelectItem && (
                  <TableCell>
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={(checked) => 
                        onSelectItem(item.id, checked === true)
                      }
                    />
                  </TableCell>
                )}
                <TableCell className="font-medium">{item.name}</TableCell>
                <TableCell>{item.description}</TableCell>
                <TableCell>${item.unitCost.toFixed(2)}</TableCell>
                <TableCell>{item.quantity}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => onEditItem(item)}>
                        <Edit className="mr-2 h-4 w-4" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem
                            onSelect={(e) => {
                              e.preventDefault();
                            }}
                          >
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This will permanently delete this item. This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => onDeleteItem(item)}>Delete</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {categories.map(category => {
        const categoryItems = itemsByCategory[category] || [];
        if (categories.length > 1 && categoryItems.length === 0) return null;
        
        return (
          <Card key={category} className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-xl">{systemCategories[category] || category}</CardTitle>
              <Button variant="outline" size="sm" onClick={() => onAddItem(category)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent>
              {categoryItems.length === 0 ? (
                <div className="text-center p-4 border rounded-md bg-muted/10">
                  <p className="text-muted-foreground">No items found for {systemCategories[category]}.</p>
                  <Button variant="link" onClick={() => onAddItem(category)}>Add an item</Button>
                </div>
              ) : viewMode === 'card' ? (
                renderCardView(category, categoryItems)
              ) : (
                renderListView(categoryItems)
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default ItemsByCategory;
