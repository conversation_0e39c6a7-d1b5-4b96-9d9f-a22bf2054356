
import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { WeQuotePackageItem } from '@/services/zapierService';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Image, Info, DollarSign } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';

interface CategoryOption {
  value: string;
  label: string;
}

interface ItemFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  currentItem: WeQuotePackageItem | null;
  availableCategories: CategoryOption[];
  onUpdateItem: (field: keyof WeQuotePackageItem, value: any) => void;
  onSave: () => void;
}

const ItemFormDialog: React.FC<ItemFormDialogProps> = ({
  open,
  onOpenChange,
  title,
  description,
  currentItem,
  availableCategories,
  onUpdateItem,
  onSave,
}) => {
  const [activeTab, setActiveTab] = useState('details');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  
  // Return early if there's no current item
  if (!currentItem) return null;
  
  // Format currency input
  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9.]/g, '');
    return numericValue;
  };

  // Handle form submission with validation
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    const errors: Record<string, string> = {};
    
    if (!currentItem.name.trim()) {
      errors.name = 'Item name is required';
    }
    
    if (!currentItem.systemCategory) {
      errors.systemCategory = 'System category is required';
    }
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    
    // Clear errors and save
    setFormErrors({});
    onSave();
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-1 mb-4 w-full bg-muted/80 backdrop-blur-sm">
              <TabsTrigger 
                value="details"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/90 data-[state=active]:to-secondary/90 data-[state=active]:text-white"
              >
                Item Details
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className={formErrors.name ? 'text-destructive' : ''}>
                    Item Name *
                  </Label>
                  <Input 
                    id="name"
                    value={currentItem.name}
                    onChange={(e) => onUpdateItem('name', e.target.value)}
                    placeholder="Enter item name"
                    className={formErrors.name ? 'border-destructive' : ''}
                    required
                  />
                  {formErrors.name && (
                    <p className="text-xs text-destructive">{formErrors.name}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="systemCategory" className={formErrors.systemCategory ? 'text-destructive' : ''}>
                    System Category *
                  </Label>
                  <Select 
                    value={currentItem.systemCategory} 
                    onValueChange={(value) => onUpdateItem('systemCategory', value)}
                  >
                    <SelectTrigger className={formErrors.systemCategory ? 'border-destructive' : ''}>
                      <SelectValue placeholder="Select system category" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCategories.map(category => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.systemCategory && (
                    <p className="text-xs text-destructive">{formErrors.systemCategory}</p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={currentItem.description || ''}
                  onChange={(e) => onUpdateItem('description', e.target.value)}
                  placeholder="Description of the item"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manufacturer">Manufacturer</Label>
                  <Input
                    id="manufacturer"
                    value={currentItem.manufacturer || ''}
                    onChange={(e) => onUpdateItem('manufacturer', e.target.value)}
                    placeholder="e.g., Sonos"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="manufacturerSku">Manufacturer SKU</Label>
                  <Input
                    id="manufacturerSku"
                    value={currentItem.manufacturerSku || ''}
                    onChange={(e) => onUpdateItem('manufacturerSku', e.target.value)}
                    placeholder="e.g., UAP-AC-PRO"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="skuNumber">SKU Number</Label>
                  <Input
                    id="skuNumber"
                    value={currentItem.skuNumber || ''}
                    onChange={(e) => onUpdateItem('skuNumber', e.target.value)}
                    placeholder="e.g., SKU123"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="imageUrl">
                    <Image className="h-4 w-4 inline mr-1" />
                    Image URL
                  </Label>
                  <Input
                    id="imageUrl"
                    value={currentItem.imageUrl || ''}
                    onChange={(e) => onUpdateItem('imageUrl', e.target.value)}
                    placeholder="https://example.com/image.jpg"
                    type="url"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="unitCost">Unit Cost *</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="unitCost"
                      value={currentItem.unitCost}
                      onChange={(e) => onUpdateItem('unitCost', formatCurrency(e.target.value))}
                      className="pl-7"
                      placeholder="0.00"
                      required
                      min="0"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    value={currentItem.quantity}
                    onChange={(e) => onUpdateItem('quantity', parseInt(e.target.value) || 1)}
                    placeholder="1"
                    required
                    min="1"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="unit">Unit</Label>
                  <Input
                    id="unit"
                    value={currentItem.unit}
                    onChange={(e) => onUpdateItem('unit', e.target.value)}
                    placeholder="piece"
                    defaultValue="piece"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="tradePrice">Trade Price</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="tradePrice"
                      value={currentItem.tradePrice || ''}
                      onChange={(e) => onUpdateItem('tradePrice', formatCurrency(e.target.value))}
                      className="pl-7"
                      placeholder="0.00"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="msrp">MSRP</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="msrp"
                      value={currentItem.msrp || ''}
                      onChange={(e) => onUpdateItem('msrp', formatCurrency(e.target.value))}
                      className="pl-7"
                      placeholder="0.00"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
          
          <Separator />
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="shadow-sm"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              variant="gradient"
              className="shadow-sm"
            >
              Save Item
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ItemFormDialog;
