
import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>l, Trash2, Info, Check, Lightbulb, PanelTop, Speaker, Monitor, Wifi, ShieldCheck, Radio } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ProductItem } from '@/hooks/usePackageManagement';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { cn } from '@/lib/utils';

interface ItemCardProps {
  item: ProductItem;
  onEdit: () => void;
  onDelete: () => void;
  isStandalone?: boolean;
  selected?: boolean;
  onSelect?: (selected: boolean) => void;
}

const ItemCard: React.FC<ItemCardProps> = ({
  item,
  onEdit,
  onDelete,
  isStandalone = false,
  selected = false,
  onSelect
}) => {
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  // Handle card click for selection
  const handleCardClick = () => {
    if (onSelect) {
      onSelect(!selected);
    }
  };

  // Handle edit button click
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onEdit();
  };

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onDelete();
  };

  // Handle tooltip info click
  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    // The tooltip is handled automatically by the TooltipProvider
  };

  // Get subcategory display names
  const getSubcategoryNames = (subcategories: string[] | undefined): string => {
    if (!subcategories || subcategories.length === 0) {
      return 'None';
    }
    return subcategories.join(', ');
  };

  // Get system category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'lighting': return <Lightbulb className="h-4 w-4 text-amber-500" />;
      case 'shades': return <PanelTop className="h-4 w-4 text-indigo-500" />;
      case 'audio': return <Speaker className="h-4 w-4 text-emerald-500" />;
      case 'video': return <Monitor className="h-4 w-4 text-blue-500" />;
      case 'network': return <Wifi className="h-4 w-4 text-sky-500" />;
      case 'security': return <ShieldCheck className="h-4 w-4 text-rose-500" />;
      case 'controlSystems': return <Radio className="h-4 w-4 text-purple-500" />;
      default: return null;
    }
  };

  // Get system category label
  const getCategoryLabel = (category: string): string => {
    const categories: Record<string, string> = {
      'lighting': 'Lighting',
      'shades': 'Shades',
      'audio': 'Audio',
      'video': 'Video',
      'network': 'Network',
      'security': 'Security',
      'controlSystems': 'Control Systems',
    };
    return categories[category] || category;
  };

  return (
    <Card 
      className={cn(
        "h-full transition-all hover:shadow-md",
        onSelect && "cursor-pointer hover:border-primary/50",
        selected && "border-primary bg-primary/5"
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <CardTitle className="line-clamp-1">{item.name}</CardTitle>
            <CardDescription className="line-clamp-1">
              {item.manufacturer && `${item.manufacturer}`}
              {item.model && item.manufacturer && ` - `}
              {item.model && `${item.model}`}
            </CardDescription>
          </div>
          
          {selected && (
            <div className="relative -mr-2 -mt-2">
              <Badge className="absolute -right-1 -top-1 flex items-center justify-center w-6 h-6 p-0 rounded-full bg-primary">
                <Check className="h-3.5 w-3.5 text-primary-foreground" />
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="pb-3">
        {item.imageUrl ? (
          <div className="mb-4 rounded-md overflow-hidden border">
            <AspectRatio ratio={4/3}>
              <img 
                src={item.imageUrl} 
                alt={item.name}
                className="object-cover w-full h-full"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder.svg'; 
                }}
              />
            </AspectRatio>
          </div>
        ) : (
          <div className="mb-4 rounded-md overflow-hidden border bg-muted/20">
            <AspectRatio ratio={4/3}>
              <div className="flex items-center justify-center w-full h-full">
                <img 
                  src="/placeholder.svg" 
                  alt="No image"
                  className="w-12 h-12 opacity-50"
                />
              </div>
            </AspectRatio>
          </div>
        )}
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">SKU:</span>
            <span className="font-medium">{item.sku || 'N/A'}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Unit Cost:</span>
            <span className="font-medium">{formatCurrency(item.unitCost)}</span>
          </div>
          
          {item.msrp > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">MSRP:</span>
              <span className="font-medium">{formatCurrency(item.msrp)}</span>
            </div>
          )}
          
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">System:</span>
            <span className="font-medium flex items-center gap-1">
              {getCategoryIcon(item.systemCategory)}
              {getCategoryLabel(item.systemCategory)}
            </span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between pt-3">
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline"
            className="shadow-sm"
            onClick={handleEditClick}
          >
            <Pencil className="h-4 w-4" />
          </Button>
          
          <Button 
            size="sm" 
            variant="outline"
            className="shadow-sm"
            onClick={handleDeleteClick}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          
          {item.description && (
            <TooltipProvider delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="shadow-sm"
                    onClick={handleInfoClick}
                    type="button"
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs p-3 bg-white">
                  <p className="text-sm">{item.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ItemCard;
