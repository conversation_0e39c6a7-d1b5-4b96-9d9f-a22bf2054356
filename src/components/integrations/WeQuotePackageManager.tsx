
import React, { useState, useEffect } from 'react';
import usePackageManagement, { systemCategories, ProductItem } from '@/hooks/usePackageManagement';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { LayoutGrid, LayoutList, Upload, Plus } from 'lucide-react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Badge } from '@/components/ui/badge';
import PackagesByCategory from './packages/PackagesByCategory';
import { WeQuotePackage } from '@/services/zapierService';
import PackageSubcategoryMapping from '../products/ProductSubcategoryMapping';
import ImportCSVDialog from './packages/ImportCSVDialog';

interface WeQuotePackageManagerProps {
  isStandalone?: boolean;
  hideHeader?: boolean;
  bulkEditMode?: boolean;
  selectedItems?: string[];
  onSelectItem?: (itemId: string, isSelected: boolean) => void;
  viewMode?: 'card' | 'list';
}

const WeQuotePackageManager: React.FC<WeQuotePackageManagerProps> = ({
  isStandalone = false,
  hideHeader = false,
  bulkEditMode = false,
  selectedItems = [],
  onSelectItem,
  viewMode = 'card'
}) => {
  const [viewModeState, setViewModeState] = useState<'card' | 'list'>(viewMode);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [isMappingDialogOpen, setIsMappingDialogOpen] = useState(false);

  const {
    items,
    packages,
    currentItem: currentPackage,
    isLoading,
    isAddDialogOpen,
    isEditDialogOpen,
    isImportCSVDialogOpen,
    handleAddItem: handleAddPackage,
    handleEditItem: handleEditPackage,
    handleDeleteItem: handleDeletePackage,
    handleUpdateItem: handleUpdatePackage,
    updateItemSubcategories: updatePackageSubcategories,
    getItemsByCategory: getPackagesByCategory,
    getAllItems: getAllPackages,
    getItemsBySubcategory: getPackagesBySubcategory,
    handleImportCSVContent,
    setIsAddDialogOpen,
    setIsEditDialogOpen,
    setIsImportCSVDialogOpen
  } = usePackageManagement({ isStandalone });

  // Get all categories for filtering
  const getAllCategories = () => {
    if (selectedCategory === 'all') {
      return Object.keys(systemCategories);
    } else {
      return [selectedCategory];
    }
  };

  // Filter items based on selected category and subcategory
  const getFilteredPackages = () => {
    if (selectedCategory === 'all') {
      if (selectedSubcategory) {
        // If subcategory is selected with "all", filter by that specific subcategory
        return getPackagesBySubcategory(selectedSubcategory);
      }
      return getAllPackages();
    } else if (selectedSubcategory) {
      // Filter by specific subcategory
      return getPackagesByCategory(selectedCategory, selectedSubcategory);
    } else {
      // Filter by category only
      return getPackagesByCategory(selectedCategory);
    }
  };

  // Handle subcategory selection
  const handleSubcategoryChange = (subcategories: string[]) => {
    if (currentPackage) {
      updatePackageSubcategories(subcategories);
    }
  };

  return (
    <div className="container max-w-7xl mx-auto">
      {!hideHeader && (
        <div className="flex items-center justify-between py-4">
          <h1 className="text-2xl font-bold">Product Packages</h1>
          <div className="flex gap-2">
            <ToggleGroup type="single" value={viewModeState} onValueChange={(value) => value && setViewModeState(value as 'card' | 'list')}>
              <ToggleGroupItem value="card" aria-label="Card View">
                <LayoutGrid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="List View">
                <LayoutList className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
            <Button onClick={() => setIsMappingDialogOpen(true)}>
              Manage Subcategories
            </Button>
            <Button onClick={handleAddPackage}>
              <Plus className="mr-2 h-4 w-4" />
              Add Package
            </Button>
          </div>
        </div>
      )}

      {/* Import options */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button variant="outline" onClick={() => setIsImportCSVDialogOpen(true)}>
          <Upload className="mr-2 h-4 w-4" />
          Import CSV
        </Button>
      </div>

      {/* Category and Subcategory Filters */}
      <div className="flex items-center space-x-4 py-2">
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Systems</SelectItem>
            {Object.entries(systemCategories).map(([key, label]) => (
              <SelectItem key={key} value={key}>{label}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Subcategory Dialog */}
        <Dialog open={isMappingDialogOpen} onOpenChange={setIsMappingDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Manage Package Subcategories</DialogTitle>
              <DialogDescription>
                Map packages to specific subcategories to improve organization and selection options
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>System Category</Label>
                <Select
                  value={currentPackage?.systemCategory || 'lighting'}
                  onValueChange={(value) => {
                    if (currentPackage) {
                      handleUpdatePackage('systemCategory', value);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select system category" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(systemCategories).map(([key, label]) => (
                      <SelectItem key={key} value={key}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {currentPackage && (
                <PackageSubcategoryMapping
                  systemCategory={currentPackage.systemCategory || 'lighting'}
                  selectedSubcategories={currentPackage.subcategories || []}
                  onChange={handleSubcategoryChange}
                />
              )}

              <div className="flex justify-end">
                <Button onClick={() => setIsMappingDialogOpen(false)}>
                  Done
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Products display */}
      <PackagesByCategory
        categories={getAllCategories()}
        packages={getFilteredPackages() as unknown as WeQuotePackage[]}
        onAddPackage={handleAddPackage}
        onEditPackage={(pkgOrId) => {
          // Handle either a package object or just an ID
          if (typeof pkgOrId === 'string') {
            const pkg = packages.find(p => p.id === pkgOrId);
            if (pkg) handleEditPackage(pkg as unknown as ProductItem);
          } else {
            handleEditPackage(pkgOrId as unknown as ProductItem);
          }
        }}
        onDeletePackage={(pkgOrId) => {
          // Handle either a package object or just an ID
          if (typeof pkgOrId === 'string') {
            const pkg = packages.find(p => p.id === pkgOrId);
            if (pkg) handleDeletePackage(pkg as unknown as ProductItem);
          } else {
            handleDeletePackage(pkgOrId as unknown as ProductItem);
          }
        }}
        isStandalone={isStandalone}
      />

      {/* Import CSV Dialog */}
      <ImportCSVDialog
        open={isImportCSVDialogOpen}
        onOpenChange={setIsImportCSVDialogOpen}
        onImport={(csvContent) => handleImportCSVContent(csvContent)}
      />
    </div>
  );
};

export default WeQuotePackageManager;
