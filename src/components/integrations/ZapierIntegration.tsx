
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Webhook } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { getZapierWebhookUrl, saveZapierWebhookUrl, sendToZapier } from '@/services/zapierService';

interface ZapierIntegrationProps {
  defaultWebhookUrl?: string;
  onComplete?: () => void;
  formData?: any; // Optional form data to send
}

export const ZapierIntegration: React.FC<ZapierIntegrationProps> = ({
  defaultWebhookUrl = '',
  onComplete,
  formData
}) => {
  // Use the global webhook URL if no default is provided
  const [webhookUrl, setWebhookUrl] = useState(defaultWebhookUrl || getZapierWebhookUrl());
  const [isLoading, setIsLoading] = useState(false);
  const [includeFormData, setIncludeFormData] = useState(true);
  
  // Customer fields based on the image
  const [customerName, setCustomerName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [addressLine1, setAddressLine1] = useState('');
  const [addressLine2, setAddressLine2] = useState('');
  const [addressLine3, setAddressLine3] = useState('');
  const [town, setTown] = useState('');
  const [county, setCounty] = useState('');
  const [postcode, setPostcode] = useState('');
  const [country, setCountry] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [emailAddress, setEmailAddress] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [notes, setNotes] = useState('');
  
  const { toast } = useToast();

  // Always use the latest stored webhook URL when the component mounts
  useEffect(() => {
    // If no default is provided, use the global one
    if (!defaultWebhookUrl) {
      const savedUrl = getZapierWebhookUrl();
      if (savedUrl) {
        setWebhookUrl(savedUrl);
      }
    }
    
    // Pre-fill form if formData is available
    if (formData) {
      if (formData.clientInfo) {
        // Pre-fill fields based on client info
        setCustomerName(formData.clientInfo.fullName || '');
        setAddressLine1(formData.clientInfo.address || '');
        setTown(formData.clientInfo.city || '');
        setCounty(formData.clientInfo.state || '');
        setPostcode(formData.clientInfo.zipCode || '');
        setPhoneNumber(formData.clientInfo.phone || '');
        setEmailAddress(formData.clientInfo.email || '');
      }
      
      // Generate a unique account number if not set
      if (!accountNumber) {
        setAccountNumber(`ACC-${Date.now().toString().slice(-6)}`);
      }
    }
  }, [defaultWebhookUrl, formData]);

  const handleTrigger = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!webhookUrl) {
      toast({
        title: "Error",
        description: "Please enter your Zapier webhook URL",
        variant: "destructive",
      });
      return;
    }

    if (!customerName) {
      toast({
        title: "Error",
        description: "Customer name is required",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    console.log("Triggering Zapier webhook:", webhookUrl);

    // Save webhook URL to localStorage for future use
    saveZapierWebhookUrl(webhookUrl);

    try {
      // Prepare payload for Zapier with all fields
      const payload: any = {
        customer_name: customerName,
        account_number: accountNumber,
        address: {
          line1: addressLine1,
          line2: addressLine2,
          line3: addressLine3,
          town: town,
          county: county,
          postcode: postcode,
          country: country
        },
        phone_number: phoneNumber,
        email_address: emailAddress,
        website_url: websiteUrl,
        notes: notes,
        client_data: {
          source: "Home Technology System Association",
          platform: "HTSA Walkthrough Tool"
        }
      };

      // If form data is provided and includeFormData is enabled, add it to the payload
      if (formData && includeFormData) {
        // Include full walkthrough data
        payload.client_data = {
          ...payload.client_data,
          client_info: formData.clientInfo || {},
          project_details: {
            budget: formData.qualificationInfo?.budget || 0,
            square_footage: formData.qualificationInfo?.squareFootage || 0,
            completion_date: formData.qualificationInfo?.completionDate || null,
            project_type: formData.clientInfo?.projectType || '',
            home_type: formData.clientInfo?.homeType || '',
          },
          rooms: formData.rooms || [],
          systems: formData.systemCategories || {},
          scope_of_work: formData.qualificationInfo?.generatedScopeOfWork || formData.executiveSummary || '',
        };
      }

      await sendToZapier(webhookUrl, payload);

      toast({
        title: "Request Sent",
        description: "The data was sent to Zapier. Please check your Zap's history to confirm it was triggered.",
      });
      
      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error("Error triggering webhook:", error);
      toast({
        title: "Error",
        description: "Failed to trigger the Zapier webhook. Please check the URL and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Webhook className="h-5 w-5" />
          Zapier Integration
        </CardTitle>
        <CardDescription>
          Connect your walkthrough data to external systems through Zapier
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleTrigger}>
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="webhook-url">Zapier Webhook URL</Label>
              <Input
                id="webhook-url"
                placeholder="https://hooks.zapier.com/hooks/catch/..."
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Enter the webhook URL from your Zapier account to connect to your workflows
              </p>
            </div>
            
            {formData && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="include-form-data" 
                    checked={includeFormData} 
                    onCheckedChange={setIncludeFormData}
                  />
                  <Label htmlFor="include-form-data">Include walkthrough data in Zapier trigger</Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  This will send client information, room details, and scope of work to Zapier
                </p>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="customer-name">Customer Name *</Label>
              <Input
                id="customer-name"
                placeholder="Enter customer name"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="account-number">Account Number</Label>
              <Input
                id="account-number"
                placeholder="Enter account number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
              />
            </div>
            
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Address Information</h3>
              
              <div className="space-y-2">
                <Label htmlFor="address-line-1">Address Line 1</Label>
                <Input
                  id="address-line-1"
                  placeholder="Enter address line 1"
                  value={addressLine1}
                  onChange={(e) => setAddressLine1(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address-line-2">Address Line 2</Label>
                <Input
                  id="address-line-2"
                  placeholder="Enter address line 2"
                  value={addressLine2}
                  onChange={(e) => setAddressLine2(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address-line-3">Address Line 3</Label>
                <Input
                  id="address-line-3"
                  placeholder="Enter address line 3"
                  value={addressLine3}
                  onChange={(e) => setAddressLine3(e.target.value)}
                />
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="town">Town</Label>
                  <Input
                    id="town"
                    placeholder="Enter town"
                    value={town}
                    onChange={(e) => setTown(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="county">County</Label>
                  <Input
                    id="county"
                    placeholder="Enter county"
                    value={county}
                    onChange={(e) => setCounty(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="postcode">Postcode</Label>
                  <Input
                    id="postcode"
                    placeholder="Enter postcode"
                    value={postcode}
                    onChange={(e) => setPostcode(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    placeholder="Enter country"
                    value={country}
                    onChange={(e) => setCountry(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone-number">Phone Number</Label>
                <Input
                  id="phone-number"
                  placeholder="Enter phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email-address">Email Address</Label>
                <Input
                  id="email-address"
                  type="email"
                  placeholder="Enter email address"
                  value={emailAddress}
                  onChange={(e) => setEmailAddress(e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="website-url">Website URL</Label>
              <Input
                id="website-url"
                placeholder="Enter website URL"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Enter additional notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
        </form>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleTrigger} 
          disabled={isLoading || !webhookUrl || !customerName}
          className="w-full sm:w-auto"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending to Zapier
            </>
          ) : (
            'Send to Zapier'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ZapierIntegration;
