
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Zap } from 'lucide-react';
import { getZapierWebhookUrl, saveZapierWebhookUrl, sendToZapier } from '@/services/zapierService';
import { Textarea } from '@/components/ui/textarea';

interface ZapierActionButtonProps {
  formData?: any;
  buttonText?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
}

const ZapierActionButton: React.FC<ZapierActionButtonProps> = ({
  formData,
  buttonText = "Send to Zapier",
  variant = "outline",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [webhookUrl, setWebhookUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  // Customer fields based on the image
  const [customerName, setCustomerName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [addressLine1, setAddressLine1] = useState('');
  const [addressLine2, setAddressLine2] = useState('');
  const [addressLine3, setAddressLine3] = useState('');
  const [town, setTown] = useState('');
  const [county, setCounty] = useState('');
  const [postcode, setPostcode] = useState('');
  const [country, setCountry] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [emailAddress, setEmailAddress] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [notes, setNotes] = useState('');
  
  const { toast } = useToast();
  
  // Load saved webhook URL from localStorage when component mounts or dialog opens
  useEffect(() => {
    if (isOpen) {
      const savedUrl = getZapierWebhookUrl();
      setWebhookUrl(savedUrl);
      
      // Generate a default account number and pre-fill form data
      if (formData) {
        setAccountNumber(`ACC-${Date.now().toString().slice(-6)}`);
        
        if (formData.clientInfo) {
          setCustomerName(formData.clientInfo.fullName || '');
          setAddressLine1(formData.clientInfo.address || '');
          setTown(formData.clientInfo.city || '');
          setCounty(formData.clientInfo.state || '');
          setPostcode(formData.clientInfo.zipCode || '');
          setPhoneNumber(formData.clientInfo.phone || '');
          setEmailAddress(formData.clientInfo.email || '');
        }
      }
    }
  }, [isOpen, formData]);
  
  const handleTriggerZap = async () => {
    if (!webhookUrl) {
      toast({
        title: "Missing Webhook URL",
        description: "Please enter a Zapier webhook URL",
        variant: "destructive",
      });
      return;
    }
    
    if (!customerName) {
      toast({
        title: "Missing Required Fields",
        description: "Please fill in all required fields marked with an asterisk (*)",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Prepare payload with customer fields
      const payload = {
        customer_name: customerName,
        account_number: accountNumber,
        address: {
          line1: addressLine1,
          line2: addressLine2,
          line3: addressLine3,
          town: town,
          county: county,
          postcode: postcode,
          country: country
        },
        phone_number: phoneNumber,
        email_address: emailAddress,
        website_url: websiteUrl,
        notes: notes,
        client_data: formData || {}
      };
      
      // Use the shared service to send data to Zapier
      await sendToZapier(webhookUrl, payload);
      
      toast({
        title: "Success",
        description: "Data sent to Zapier successfully",
      });
      
      // Close the dialog after successful submission
      setIsOpen(false);
    } catch (error) {
      console.error("Error sending data to Zapier:", error);
      toast({
        title: "Error",
        description: "Failed to send data to Zapier",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <>
      <Button 
        variant={variant} 
        onClick={() => setIsOpen(true)}
        className={`flex items-center gap-2 ${className}`}
      >
        <Zap className="h-4 w-4" />
        {buttonText}
      </Button>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Send to Zapier</DialogTitle>
            <DialogDescription>
              Enter the required information to send your data to Zapier.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="zapier-webhook">Zapier Webhook URL</Label>
              <Input
                id="zapier-webhook"
                placeholder="https://hooks.zapier.com/hooks/catch/..."
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                You can find this URL in your Zapier account when creating a webhook trigger.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="customer-name">Customer Name *</Label>
              <Input
                id="customer-name"
                placeholder="Enter customer name"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="account-number">Account Number</Label>
              <Input
                id="account-number"
                placeholder="Enter account number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="address-line-1">Address Line 1</Label>
                <Input
                  id="address-line-1"
                  placeholder="Enter address line 1"
                  value={addressLine1}
                  onChange={(e) => setAddressLine1(e.target.value)}
                />
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="town">Town</Label>
                  <Input
                    id="town"
                    placeholder="Enter town"
                    value={town}
                    onChange={(e) => setTown(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="postcode">Postcode</Label>
                  <Input
                    id="postcode"
                    placeholder="Enter postcode"
                    value={postcode}
                    onChange={(e) => setPostcode(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone-number">Phone Number</Label>
                <Input
                  id="phone-number"
                  placeholder="Enter phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email-address">Email Address</Label>
                <Input
                  id="email-address"
                  type="email"
                  placeholder="Enter email address"
                  value={emailAddress}
                  onChange={(e) => setEmailAddress(e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Add any additional notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleTriggerZap}
              disabled={isLoading || !webhookUrl || !customerName}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending
                </>
              ) : (
                'Send to Zapier'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ZapierActionButton;
