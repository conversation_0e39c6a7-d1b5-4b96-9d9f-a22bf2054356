
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { 
  saveZohoOAuthConfig, 
  getZohoOAuthConfig,
  isAuthenticated,
  initiateOAuthFlow,
  clearZohoOAuthTokens
} from '@/services/zohoOAuthService';
import { Link, ExternalLink, Save, Key } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const ZohoOAuthConfig: React.FC = () => {
  const { toast } = useToast();
  const [clientId, setClientId] = useState<string>('');
  const [clientSecret, setClientSecret] = useState<string>('');
  const [isConnected, setIsConnected] = useState<boolean>(false);

  useEffect(() => {
    // Load saved configuration on component mount
    const config = getZohoOAuthConfig();
    setClientId(config.clientId || '');
    setClientSecret(config.clientSecret || '');
    
    // Check if already authenticated
    setIsConnected(isAuthenticated());
  }, []);

  const handleSaveConfig = () => {
    if (!clientId || !clientSecret) {
      toast({
        title: "Error",
        description: "Client ID and Client Secret are required",
        variant: "destructive",
      });
      return;
    }
    
    try {
      saveZohoOAuthConfig({ clientId, clientSecret });
      toast({
        title: "Configuration Saved",
        description: "Zoho CRM OAuth configuration has been saved",
      });
    } catch (error) {
      console.error("Failed to save configuration:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save configuration",
        variant: "destructive",
      });
    }
  };
  
  const handleConnect = () => {
    if (!clientId || !clientSecret) {
      toast({
        title: "Error",
        description: "Client ID and Client Secret are required",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Save configuration before initiating OAuth flow
      saveZohoOAuthConfig({ 
        clientId, 
        clientSecret,
        scope: ['ZohoCRM.modules.ALL', 'ZohoCRM.settings.ALL', 'ZohoCRM.users.ALL', 'ZohoContacts.contacts.ALL']
      });
      
      // Start OAuth flow
      initiateOAuthFlow();
    } catch (error) {
      console.error("Failed to initiate OAuth flow:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to connect to Zoho CRM",
        variant: "destructive",
      });
    }
  };
  
  const handleDisconnect = () => {
    try {
      clearZohoOAuthTokens();
      setIsConnected(false);
      toast({
        title: "Disconnected",
        description: "Successfully disconnected from Zoho CRM",
      });
    } catch (error) {
      console.error("Failed to disconnect:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to disconnect from Zoho CRM",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Zoho CRM OAuth Configuration</h3>
        {isConnected && (
          <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
            Connected
          </span>
        )}
      </div>
      
      <Alert className="bg-amber-50 border-amber-200">
        <AlertTitle className="text-amber-800">Configuration Note</AlertTitle>
        <AlertDescription className="text-amber-700 text-sm">
          Make sure to set the correct redirect URI in your Zoho API Console. The redirect URI must be exactly:
          <code className="block mt-1 p-2 bg-white border border-amber-200 rounded text-xs font-mono">
            {window.location.origin}/settings?auth=zoho
          </code>
        </AlertDescription>
      </Alert>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="zoho-client-id">Client ID</Label>
          <Input
            id="zoho-client-id"
            placeholder="Enter your Zoho Client ID"
            value={clientId}
            onChange={(e) => setClientId(e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="zoho-client-secret">Client Secret</Label>
          <Input
            id="zoho-client-secret"
            type="password"
            placeholder="Enter your Zoho Client Secret"
            value={clientSecret}
            onChange={(e) => setClientSecret(e.target.value)}
          />
        </div>
        
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            onClick={handleSaveConfig}
            className="flex items-center gap-1"
          >
            <Save className="h-4 w-4" />
            Save Config
          </Button>
          
          {isConnected ? (
            <Button 
              variant="destructive" 
              onClick={handleDisconnect}
              className="flex items-center gap-1"
            >
              Disconnect
            </Button>
          ) : (
            <Button 
              onClick={handleConnect}
              className="flex-1 bg-[#EA4335] hover:bg-[#D23C2C] text-white flex items-center gap-1"
            >
              <img 
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAADGklEQVQ4jV2TXWjWdRjHP8//ef6/57fb89+e59k2l865zRctomJEFymBRKQRkgV2E4QR3ZRdJHZR3QhJF0FEeNNFF0EXUpYgBRLoEFF0UxARlc0pOl+mc25u2/P+n+cvzwIrP3f5en8+H76KlNIaGKIcjbK6YYKdY1Mcd5HSVsQuhZRQryLptpjnOsvT3e5L4+Gjl1b52VcpAj/5lIB93bDnGecONNN8IOJrUkotKwXSPyHX4vIFZn5CXeuCzOxpRF65cQr2vwzb7wJe1fDL28qFY8pCzSIqdQjACYSMtgJ1NrG0p8n49iG9tOPwPuw/t8C25yIcbikXDytXZyJZFrG1DCJgDaFuBaR98KmLMnu4wY2bAg5/CNuehcbbst5D6sIpZfbPSHneEGsSTrKQkkBwebCaOX+X8eWhJtMbA2YUvLcZpi/KZw9GzjcMTsJAThIiu21iaxETM7OLrM4DOttPD+ldxTZmoOshYfmK4CYCfQdgNy+ty5jvr5KFGqQYoWFIQB0j3AJkGQwWJOxthLfnazx9Og+nhisoTqsWlt5Efp6WcDtCayjMJWKrH9kREAgQWyi4JKuz0EwWn69XOTvZy7UzwuI6D30ViEtYiGRpjoIbJDGHEsw5dxP8iOCAgYAEa/y6cIVn/13iwd4JuoWl94XVCShcC0QR67P05AZJKJYYi6bB9201uwh918wIS+3lvUQlDm5J6K+tMbTTkPMtq2vqrA8VXa5MiS5SiASA89Dbqzw/WKLnL96ofLj5QsDxpMrJu8vDdzZJrVqRp25zieuHGf99Rai/Ioxt0AxMJlQvJ1RDdAaWAlw8i52YIvvhAlvyiV3Ffvb1jrN/34/kygllqm9yjl1/KmNBpKi188LJSW7t89yfz/BNDLEtoecNezxhBzc4N/wHnbZF//tb6Vhscyu3TY7JgW/+fei1MJfILzqigDQy/BHvVCd4uoV8eTeeQOf38Y+dcfed0tTb+ULW5TbZrtu97e7XKe1Lnpa401SkvjLcrjxzvuPS21f1yV/9WZpvsSPr6RGe3ZT+L/8BM373De0rIgMAAAAASUVORK5CYII=" 
                className="mr-2 h-4 w-4"
                alt="Zoho logo"
              />
              Connect to Zoho CRM
            </Button>
          )}
        </div>
      </div>
      
      <div className="text-xs text-muted-foreground mt-2">
        <p>To create Zoho CRM API credentials:</p>
        <ol className="list-decimal pl-5 space-y-1">
          <li>Go to <a href="https://api-console.zoho.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Zoho API Console</a></li>
          <li>Create a new Client (Self Client)</li>
          <li>Set the redirect URI to: <span className="font-mono text-xs bg-gray-100 p-1 rounded">
            {window.location.origin}/settings?auth=zoho
          </span></li>
          <li>Copy the Client ID and Client Secret here</li>
        </ol>
      </div>
      
      <div className="text-xs text-muted-foreground mt-2 p-2 border rounded bg-gray-50">
        <p className="font-medium">Required Scopes:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>ZohoCRM.modules.ALL</li>
          <li>ZohoCRM.settings.ALL</li>
          <li>ZohoCRM.users.ALL</li>
          <li>ZohoContacts.contacts.ALL</li>
        </ul>
      </div>
    </div>
  );
};

export default ZohoOAuthConfig;
