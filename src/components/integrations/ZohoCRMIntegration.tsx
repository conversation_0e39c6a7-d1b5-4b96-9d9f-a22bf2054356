
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useFormContext } from '../../context/FormContext';
import { ExternalLink, Link } from 'lucide-react';
import { sendToZohoCRM } from '@/services/zohoCrmService';
import { isAuthenticated } from '@/services/zohoOAuthService';

const ZohoCRMIntegration: React.FC = () => {
  const { formData } = useFormContext();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dealCreated, setDealCreated] = useState<boolean>(false);
  const [dealUrl, setDealUrl] = useState<string>('');
  const [isConnected, setIsConnected] = useState<boolean>(false);

  useEffect(() => {
    // Check if authenticated with Zoho CRM
    setIsConnected(isAuthenticated());
  }, []);

  const handleCreateDeal = async () => {
    if (!isConnected) {
      toast({
        title: "Not Connected",
        description: "Please connect to Zoho CRM via OAuth in the settings page.",
        variant: "destructive",
      });
      return;
    }
    
    // Check if we have the required client info
    const { clientInfo } = formData;
    const fullName = clientInfo.fullName || `${clientInfo.firstName} ${clientInfo.lastName}`.trim();
    
    if (!fullName || !clientInfo.email) {
      toast({
        title: "Missing Information",
        description: "Client name and email are required to create a deal",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Use OAuth authentication (pass true to indicate OAuth usage)
      const response = await sendToZohoCRM(true, formData);
      
      if (response.success) {
        setDealCreated(true);
        if (response.dealUrl) {
          setDealUrl(response.dealUrl);
        }
        toast({
          title: "Deal Created",
          description: response.message,
        });
      } else {
        throw new Error(response.message || "Failed to create deal");
      }
    } catch (error) {
      console.error("Error creating deal:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create deal. Please check your connection and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {!isConnected && (
        <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Not Connected to Zoho CRM</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Please connect to Zoho CRM via OAuth in the settings page before creating deals.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {dealCreated && dealUrl ? (
        <div className="rounded-md bg-green-50 p-4 border border-green-200">
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                <Link className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Deal Created Successfully</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>Your deal has been created in Zoho CRM.</p>
              </div>
              <div className="mt-4">
                <a 
                  href={dealUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <ExternalLink className="mr-2 h-4 w-4" /> View Deal in Zoho CRM
                </a>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <Button
          onClick={handleCreateDeal}
          disabled={isLoading || !isConnected}
          className="w-full bg-[#EA4335] hover:bg-[#D23C2C] text-white"
        >
          {isLoading ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
              Creating Deal...
            </>
          ) : (
            <>
              <img 
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAADGklEQVQ4jV2TXWjWdRjHP8//ef6/57fb89+e59k2l865zRctomJEFymBRKQRkgV2E4QR3ZRdJHZR3QhJF0FEeNNFF0EXUpYgBRLoEFF0UxARlc0pOl+mc25u2/P+n+cvzwIrP3f5en8+H76KlNIaGKIcjbK6YYKdY1Mcd5HSVsQuhZRQryLptpjnOsvT3e5L4+Gjl1b52VcpAj/5lIB93bDnGecONNN8IOJrUkotKwXSPyHX4vIFZn5CXeuCzOxpRF65cQr2vwzb7wJe1fDL28qFY8pCzSIqdQjACYSMtgJ1NrG0p8n49iG9tOPwPuw/t8C25yIcbikXDytXZyJZFrG1DCJgDaFuBaR98KmLMnu4wY2bAg5/CNuehcbbst5D6sIpZfbPSHneEGsSTrKQkkBwebCaOX+X8eWhJtMbA2YUvLcZpi/KZw9GzjcMTsJAThIiu21iaxETM7OLrM4DOttPD+ldxTZmoOshYfmK4CYCfQdgNy+ty5jvr5KFGqQYoWFIQB0j3AJkGQwWJOxthLfnazx9Og+nhisoTqsWlt5Efp6WcDtCayjMJWKrH9kREAgQWyi4JKuz0EwWn69XOTvZy7UzwuI6D30ViEtYiGRpjoIbJDGHEsw5dxP8iOCAgYAEa/y6cIVn/13iwd4JuoWl94XVCShcC0QR67P05AZJKJYYi6bB9201uwh918wIS+3lvUQlDm5J6K+tMbTTkPMtq2vqrA8VXa5MiS5SiASA89Dbqzw/WKLnL96ofLj5QsDxpMrJu8vDdzZJrVqRp25zieuHGf99Rai/Ioxt0AxMJlQvJ1RDdAaWAlw8i52YIvvhAlvyiV3Ffvb1jrN/34/kygllqm9yjl1/KmNBpKi188LJSW7t89yfz/BNDLEtoecNezxhBzc4N/wHnbZF//tb6Vhscyu3TY7JgW/+fei1MJfILzqigDQy/BHvVCd4uoV8eTeeQOf38Y+dcfed0tTb+ULW5TbZrtu97e7XKe1Lnpa401SkvjLcrjxzvuPS21f1yV/9WZpvsSPr6RGe3ZT+L/8BM373De0rIgMAAAAASUVORK5CYII=" 
                className="mr-2 h-4 w-4"
                alt="Zoho logo"
              />
              Create Deal in Zoho CRM
            </>
          )}
        </Button>
      )}

      <div className="text-xs text-muted-foreground mt-6 p-3 bg-gray-50 rounded-md border">
        <h4 className="font-medium mb-1">Zoho CRM API Documentation</h4>
        <p className="mb-2">For more information about the Zoho CRM API, visit:</p>
        <a 
          href="https://www.zoho.com/crm/developer/docs/api/v2/" 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline"
        >
          Zoho CRM API Documentation
        </a>
      </div>
    </div>
  );
};

export default ZohoCRMIntegration;
