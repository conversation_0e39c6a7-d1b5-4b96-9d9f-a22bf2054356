import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useFormContext } from '../../context/FormContext';
import { ExternalLink, Link } from 'lucide-react';
import axios from 'axios';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent } from '../ui/card';
import { Checkbox } from '../ui/checkbox';

interface WeQuoteResponse {
  success: boolean;
  message: string;
  quoteUrl?: string;
}

const WeQuoteIntegration: React.FC = () => {
  const { formData } = useFormContext();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [quoteCreated, setQuoteCreated] = useState<boolean>(false);
  const [quoteUrl, setQuoteUrl] = useState<string>('');
  const [apiKey, setApiKey] = useState<string>('');
  const [showApiKey, setShowApiKey] = useState<boolean>(false);
  const [includeRooms, setIncludeRooms] = useState<boolean>(true);
  const [includeSystems, setIncludeSystems] = useState<boolean>(true);

  const sendToWeQuote = async (): Promise<WeQuoteResponse> => {
    try {
      const { clientInfo, qualificationInfo, systemCategories, rooms } = formData;
      
      // Format the data for WeQuote
      const quoteData = {
        client: {
          name: clientInfo.fullName || `${clientInfo.firstName} ${clientInfo.lastName}`,
          email: clientInfo.email,
          phone: clientInfo.phone,
          address: clientInfo.address || '',
          city: clientInfo.city || '',
          state: clientInfo.state || '',
          zipCode: clientInfo.zipCode || '',
          company: clientInfo.company || '',
        },
        project: {
          budget: qualificationInfo.budget,
          timeline: qualificationInfo.timeline || '',
          purpose: qualificationInfo.purpose || '',
          projectStage: qualificationInfo.projectStage || '',
          squareFootage: qualificationInfo.squareFootage || 0,
          completionDate: qualificationInfo.completionDate ? 
            (typeof qualificationInfo.completionDate === 'string' ? 
              qualificationInfo.completionDate : 
              qualificationInfo.completionDate.toISOString().split('T')[0]) : 
            '',
        },
        systems: includeSystems ? Object.entries(systemCategories)
          .filter(([_, selected]) => selected)
          .map(([name]) => name) : [],
        rooms: includeRooms ? rooms.map(room => ({
          name: room.name,
          systems: Object.entries(room.systemSettings)
            .filter(([_, selected]) => selected)
            .map(([name]) => name)
        })) : [],
        apiKey: apiKey
      };

      // In a real implementation, this would call the WeQuote API
      // const response = await axios.post('https://api.wequote.com/v1/quotes', quoteData);
      
      // For demo purposes, simulate a successful response
      console.log("Would send to WeQuote:", quoteData);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulated successful response
      return {
        success: true,
        message: "Successfully created quote in WeQuote",
        quoteUrl: "https://app.wequote.com/quotes/demo-123456",
      };
    } catch (error) {
      console.error("Error creating WeQuote:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  };

  const handleCreateQuote = async () => {
    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please enter your WeQuote API key",
        variant: "destructive",
      });
      return;
    }
    
    if (!formData.clientInfo.firstName || !formData.clientInfo.email) {
      toast({
        title: "Missing Information",
        description: "Client name and email are required to create a quote",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await sendToWeQuote();
      
      if (response.success) {
        setQuoteCreated(true);
        if (response.quoteUrl) {
          setQuoteUrl(response.quoteUrl);
        }
        toast({
          title: "Quote Created",
          description: response.message,
        });
      } else {
        throw new Error(response.message || "Failed to create quote");
      }
    } catch (error) {
      console.error("Error creating quote:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create quote. Please check your API key and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="weQuote-key">WeQuote API Key</Label>
              <Input
                id="weQuote-key"
                type={showApiKey ? "text" : "password"}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your WeQuote API key"
              />
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowApiKey(!showApiKey)}
                className="mt-2"
              >
                {showApiKey ? "Hide API Key" : "Show API Key"}
              </Button>
            </div>
            
            <div className="space-y-2">
              <Label>Data to Include</Label>
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-rooms" 
                    checked={includeRooms} 
                    onCheckedChange={(checked) => setIncludeRooms(checked as boolean)} 
                  />
                  <Label htmlFor="include-rooms">Include Room Details</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-systems" 
                    checked={includeSystems} 
                    onCheckedChange={(checked) => setIncludeSystems(checked as boolean)} 
                  />
                  <Label htmlFor="include-systems">Include System Categories</Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {quoteCreated && quoteUrl ? (
        <div className="rounded-md bg-green-50 p-4 border border-green-200">
          <div className="flex">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                <Link className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Quote Created Successfully</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>Your quote has been created in WeQuote.</p>
              </div>
              <div className="mt-4">
                <a 
                  href={quoteUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <ExternalLink className="mr-2 h-4 w-4" /> View Quote in WeQuote
                </a>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <Button
          onClick={handleCreateQuote}
          disabled={isLoading || !apiKey}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isLoading ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
              Creating Quote...
            </>
          ) : (
            <>
              <img 
                src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDhINFY2SDIwVjhaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjAgMThINFYxNkgyMFYxOFoiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0yMCAxM0g0VjExSDIwVjEzWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==" 
                className="mr-2 h-4 w-4"
                alt="WeQuote logo"
              />
              Create Quote in WeQuote
            </>
          )}
        </Button>
      )}

      <div className="text-xs text-muted-foreground mt-6 p-3 bg-gray-50 rounded-md border">
        <h4 className="font-medium mb-1">WeQuote Integration</h4>
        <p className="mb-2">This integration allows you to send client and project data directly to WeQuote for quote generation.</p>
        <a 
          href="https://wequote.com/api-docs" 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline"
        >
          WeQuote API Documentation
        </a>
      </div>
    </div>
  );
};

export default WeQuoteIntegration;
