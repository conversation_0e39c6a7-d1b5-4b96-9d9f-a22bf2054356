
import { <PERSON>, <PERSON>, Palette as <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useTheme } from "@/components/theme/ThemeProvider";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Toggle } from "@/components/ui/toggle";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="rounded-full">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="rounded-lg shadow-lg">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("neon")}>
          Neon
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("midnight")}>
          Midnight
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("oceanic")}>
          Oceanic
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("cyborg")}>
          Cyborg
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("aurora")}>
          Aurora
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export function SimpleThemeToggle() {
  const { theme, themeMode, setTheme } = useTheme();
  
  const toggleTheme = () => {
    setTheme(themeMode === "dark" ? "light" : "dark");
  };
  
  return (
    <div 
      className="inline-flex h-10 w-20 cursor-pointer items-center rounded-full bg-slate-100 p-1 shadow-inner dark:bg-slate-800 transition-all duration-300"
      onClick={toggleTheme}
      role="button"
      tabIndex={0}
    >
      <div className={`flex h-8 w-8 transform items-center justify-center rounded-full transition-transform duration-300 ${themeMode === "dark" ? "translate-x-10 bg-slate-700 text-slate-100" : "bg-blue-500 text-white"}`}>
        {themeMode === "dark" ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
      </div>
      <span className="sr-only">Toggle theme</span>
    </div>
  );
}

export function ThemePalette() {
  const { theme } = useTheme();
  
  return (
    <PaletteIcon 
      className="h-[1.2rem] w-[1.2rem]" 
      style={{ 
        opacity: theme !== "light" && theme !== "dark" && theme !== "system" ? 1 : 0.5 
      }} 
    />
  );
}
