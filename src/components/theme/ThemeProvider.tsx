
import * as React from "react";
import { createContext, useContext, useEffect, useState } from "react";

export type Theme = "light" | "dark" | "system" | "neon" | "midnight" | "oceanic" | "cyborg" | "aurora";
export type ThemeMode = "light" | "dark"; // Base mode for each theme

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  themeMode: ThemeMode;
  setTheme: (theme: Theme) => void;
};

const initialState: ThemeProviderState = {
  theme: "system",
  themeMode: "light",
  setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "vite-ui-theme",
  ...props
}: ThemeProviderProps) {
  // Fix: Initialize state with a function for SSR compatibility
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof localStorage !== 'undefined') {
      const storedTheme = localStorage.getItem(storageKey);
      return (storedTheme as Theme) || defaultTheme;
    }
    return defaultTheme;
  });

  // Determine if a theme is light or dark base
  const getThemeMode = (currentTheme: Theme): ThemeMode => {
    if (typeof window === 'undefined') {
      // Default to light when not in browser
      return "light";
    }

    if (currentTheme === "system") {
      return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    
    // These themes are dark-based
    if (["dark", "neon", "midnight", "cyborg", "aurora"].includes(currentTheme)) {
      return "dark";
    }
    
    // These themes are light-based
    return "light";
  };

  const [themeMode, setThemeMode] = useState<ThemeMode>(getThemeMode(theme));

  useEffect(() => {
    const root = window.document.documentElement;
    const newThemeMode = getThemeMode(theme);
    
    // Remove all theme classes
    root.classList.remove(
      "light", "dark", "theme-default", "theme-neon", 
      "theme-midnight", "theme-oceanic", "theme-cyborg", "theme-aurora"
    );
    
    // Add base light/dark class
    root.classList.add(newThemeMode);
    
    // Add specific theme class
    if (theme === "system") {
      root.classList.add("theme-default");
    } else {
      root.classList.add(`theme-${theme}`);
    }
    
    setThemeMode(newThemeMode);
  }, [theme]);

  const value = {
    theme,
    themeMode,
    setTheme: (theme: Theme) => {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(storageKey, theme);
      }
      setTheme(theme);
    },
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  
  return context;
};
