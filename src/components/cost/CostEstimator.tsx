import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormData } from '@/types/formTypes';
import { Calculator, Download, Save } from 'lucide-react';
import { generateCostSummary } from '@/services/costService';
import { useToast } from '@/hooks/use-toast';
import { generateProjectPDF } from '@/utils/pdfGenerator';
import { qualityTiers, costMatrixCategories, calculateProjectCost } from '@/data/costMatrix';

interface CostEstimatorProps {
  clientData: FormData;
}

export const CostEstimator: React.FC<CostEstimatorProps> = ({ clientData }) => {
  const { clientInfo, qualificationInfo, systemCategories, rooms } = clientData;
  const [selectedTier, setSelectedTier] = useState<string>("standard");
  const [customMultiplier, setCustomMultiplier] = useState<number>(1.0);
  const [overrideBaseAmount, setOverrideBaseAmount] = useState<boolean>(false);
  const [baseAmount, setBaseAmount] = useState<number>(0);
  const [costBreakdown, setCostBreakdown] = useState<{[key: string]: number}>({});
  const [totalCost, setTotalCost] = useState<number>(0);
  const [isPdfGenerating, setIsPdfGenerating] = useState<boolean>(false);
  const { toast } = useToast();

  useEffect(() => {
    // Calculate cost based on matrix data
    const squareFootage = qualificationInfo.squareFootage || 3000;
    
    // Use the calculation function from the matrix
    const result = calculateProjectCost(
      systemCategories, 
      selectedTier, 
      squareFootage
    );
    
    if (!overrideBaseAmount) {
      // This is just an estimation of base amount for display
      const tierMultiplier = qualityTiers[selectedTier as keyof typeof qualityTiers]?.multiplier || 1.0;
      setBaseAmount(squareFootage * (selectedTier === 'premium' ? 16 : selectedTier === 'basic' ? 8 : 12));
    }
    
    // Apply custom multiplier to all costs
    const adjustedBreakdown: {[key: string]: number} = {};
    let adjustedTotal = 0;
    
    Object.entries(result.breakdown).forEach(([system, amount]) => {
      const adjustedAmount = amount * customMultiplier;
      adjustedBreakdown[system] = adjustedAmount;
      adjustedTotal += adjustedAmount;
    });
    
    setCostBreakdown(adjustedBreakdown);
    setTotalCost(adjustedTotal);
  }, [selectedTier, customMultiplier, baseAmount, overrideBaseAmount, systemCategories, qualificationInfo.squareFootage]);

  const handleTierChange = (value: string) => {
    setSelectedTier(value);
  };

  const handleBaseAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setBaseAmount(value);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getSystemLabel = (key: string): string => {
    const category = costMatrixCategories.find(cat => cat.id === key);
    return category ? category.name : key.charAt(0).toUpperCase() + key.slice(1);
  };

  const handleSaveEstimate = () => {
    // In a real app, this would save to a database
    // For now, we'll just show a success toast
    toast({
      title: "Estimate Saved",
      description: `Cost estimate of ${formatCurrency(totalCost)} has been saved.`,
    });
    
    // Optional: Save to localStorage for demo purposes
    try {
      const estimateData = {
        clientName: clientInfo.fullName,
        date: new Date().toISOString(),
        totalCost,
        breakdown: costBreakdown,
        tier: selectedTier,
        multiplier: customMultiplier,
      };
      
      // Get existing estimates or initialize empty array
      const savedEstimates = JSON.parse(localStorage.getItem('costEstimates') || '[]');
      savedEstimates.push(estimateData);
      localStorage.setItem('costEstimates', JSON.stringify(savedEstimates));
      
      console.log("Cost estimate saved:", estimateData);
    } catch (error) {
      console.error("Error saving estimate to localStorage:", error);
    }
  };

  const handleExportToPDF = () => {
    // Prevent multiple clicks
    if (isPdfGenerating) {
      return;
    }
    
    try {
      setIsPdfGenerating(true);
      
      // Create a modified version of the client data with cost information
      const enrichedFormData = {
        ...clientData,
        costEstimate: {
          totalCost,
          breakdown: costBreakdown,
          tier: selectedTier,
          multiplier: customMultiplier,
          generated: new Date().toISOString(),
        }
      };
      
      // Generate the PDF using the utility function
      generateProjectPDF(enrichedFormData);
      
      toast({
        title: "PDF Generated",
        description: "Cost estimate PDF has been generated and is ready to download.",
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Reset the state after a delay to prevent accidental double-clicks
      setTimeout(() => {
        setIsPdfGenerating(false);
      }, 1000);
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="estimate" className="w-full">
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="estimate">Budget Estimate</TabsTrigger>
          <TabsTrigger value="breakdown">System Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="estimate" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="mr-2 h-5 w-5" />
                HTSA Budget Parameters
              </CardTitle>
              <CardDescription>
                Adjust the parameters to customize your budget estimate
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tier-select">Budget Tier</Label>
                <Select 
                  value={selectedTier} 
                  onValueChange={(value) => setSelectedTier(value)}
                >
                  <SelectTrigger id="tier-select">
                    <SelectValue placeholder="Select tier" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(qualityTiers).map(tier => (
                      <SelectItem key={tier.name} value={tier.name}>
                        {tier.label} - {tier.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="custom-multiplier">Quality Multiplier: {customMultiplier.toFixed(1)}x</Label>
                </div>
                <Slider 
                  id="custom-multiplier"
                  min={0.5} 
                  max={2.0} 
                  step={0.1} 
                  value={[customMultiplier]} 
                  onValueChange={([value]) => setCustomMultiplier(value)}
                />
                <p className="text-xs text-muted-foreground">
                  Adjust this multiplier to account for the quality level of components and installation.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <input 
                      type="checkbox" 
                      id="override-base"
                      checked={overrideBaseAmount}
                      onChange={(e) => setOverrideBaseAmount(e.target.checked)} 
                      className="h-4 w-4 rounded border-gray-300" 
                    />
                    <Label htmlFor="override-base" className="text-sm font-normal">Override Base Amount</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Enable to manually set the base amount instead of using the calculated value.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="base-amount">Base Amount</Label>
                  <Input
                    id="base-amount"
                    type="number"
                    value={baseAmount}
                    onChange={(e) => setBaseAmount(parseFloat(e.target.value) || 0)}
                    disabled={!overrideBaseAmount}
                    min={0}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Client</p>
                  <p className="text-muted-foreground">{clientInfo.fullName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Project Type</p>
                  <p className="text-muted-foreground">{clientInfo.projectType || 'Not specified'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Square Footage</p>
                  <p className="text-muted-foreground">{qualificationInfo.squareFootage.toLocaleString()} sq ft</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Number of Rooms</p>
                  <p className="text-muted-foreground">{rooms.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cost Estimate</CardTitle>
              <CardDescription>
                Based on HTSA budget standards and your selections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-4xl font-bold">{formatCurrency(totalCost)}</p>
                <p className="text-muted-foreground mt-2">Estimated Project Cost</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <Button onClick={handleSaveEstimate} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save Estimate
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleExportToPDF} 
                  disabled={isPdfGenerating}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {isPdfGenerating ? 'Generating...' : 'Export to PDF'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>System Budget Breakdown</CardTitle>
              <CardDescription>
                How the total budget is allocated across different systems
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Object.entries(costBreakdown).length > 0 ? (
                <div className="space-y-6">
                  {Object.entries(costBreakdown).map(([system, amount]) => (
                    <div key={system} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label>{getSystemLabel(system)}</Label>
                        <span className="font-medium">{formatCurrency(amount)}</span>
                      </div>
                      <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-green-500 rounded-full"
                          style={{ width: `${(amount / totalCost) * 100}%` }}
                        />
                      </div>
                      <p className="text-xs text-right text-muted-foreground">
                        {Math.round((amount / totalCost) * 100)}% of total budget
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-center text-muted-foreground py-6">
                  No systems selected. Please select systems in the previous steps.
                </p>
              )}
              
              <div className="mt-8 pt-4 border-t">
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Estimate</span>
                  <span>{formatCurrency(totalCost)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Budget Tiers Explanation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium">Good (${qualityTiers.basic.multiplier * 8}/sq ft)</h3>
                <p className="text-sm text-muted-foreground">
                  Entry-level systems with basic functionality. Ideal for rental properties or simple automation needs.
                </p>
              </div>
              <div>
                <h3 className="font-medium">Better (${qualityTiers.standard.multiplier * 12}/sq ft)</h3>
                <p className="text-sm text-muted-foreground">
                  Mid-range systems with enhanced features. Suitable for most residential applications with good performance.
                </p>
              </div>
              <div>
                <h3 className="font-medium">Best (${qualityTiers.premium.multiplier * 16}/sq ft)</h3>
                <p className="text-sm text-muted-foreground">
                  Premium systems with top-tier components and features. Designed for luxury homes requiring the highest quality.
                </p>
              </div>
              <p className="text-xs italic mt-4">
                Note: These are industry standard guidelines based on HTSA recommendations. Actual costs may vary based on specific project requirements.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
