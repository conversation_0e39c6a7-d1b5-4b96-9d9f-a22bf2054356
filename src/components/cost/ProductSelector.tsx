
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, PackageSearch } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CostLineItem } from '@/models/CostStructure';
import { supabase } from '@/integrations/supabase/client';

interface Product {
  id: string;
  name: string;
  description: string | null;
  system_category: string;
  unit_cost: number;
  unit: string;
  subcategories: string[] | null;
  quantity: number;
  manufacturer: string | null;
  model: string | null;
}

interface ProductSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onProductSelect: (products: CostLineItem[]) => void;
}

const ProductSelector: React.FC<ProductSelectorProps> = ({ isOpen, onClose, onProductSelect }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Record<string, boolean>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [systemCategories, setSystemCategories] = useState<Record<string, string>>({});

  // Load products on mount
  useEffect(() => {
    if (isOpen) {
      fetchProducts();
      loadSystemCategories();
    } else {
      // Reset selection when dialog closes
      setSelectedProducts({});
      setSearchTerm('');
      setCategoryFilter('all');
    }
  }, [isOpen]);

  const loadSystemCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('system_category, name')
        .order('name');

      if (!error && data) {
        const categories: Record<string, string> = {};
        data.forEach(item => {
          categories[item.system_category] = item.name;
        });
        setSystemCategories(categories);
      }
    } catch (error) {
      console.error('Error loading system categories:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('product_catalog_items')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching products:', error);
        return;
      }

      setProducts(data || []);
    } catch (error) {
      console.error('Error in fetch process:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleProductSelection = (productId: string) => {
    setSelectedProducts(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  const handleSelectAll = (filtered: Product[]) => {
    const newSelection = { ...selectedProducts };
    filtered.forEach(product => {
      newSelection[product.id] = true;
    });
    setSelectedProducts(newSelection);
  };

  const handleUnselectAll = (filtered: Product[]) => {
    const newSelection = { ...selectedProducts };
    filtered.forEach(product => {
      delete newSelection[product.id];
    });
    setSelectedProducts(newSelection);
  };

  const handleAddSelected = () => {
    const selectedItems = products
      .filter(product => selectedProducts[product.id])
      .map(product => ({
        id: `ITEM-${product.id}`,
        name: product.name,
        category: product.system_category,
        description: product.description || undefined,
        unitCost: product.unit_cost,
        quantity: 1,
        unit: product.unit,
        // Default values for line items
        markup: 15,
        laborHours: 0,
        laborRate: 85,
        notes: `Product ID: ${product.id}`
      }));

    if (selectedItems.length > 0) {
      onProductSelect(selectedItems);
    }
    onClose();
  };

  // Filter products based on search term and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = 
      searchTerm === '' || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = 
      categoryFilter === 'all' || 
      product.system_category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const getCategoryName = (categoryKey: string) => {
    return systemCategories[categoryKey] || categoryKey;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Select Products from Catalog</DialogTitle>
          <DialogDescription>
            Choose products to add as line items to your cost structure
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-grow">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {Object.entries(systemCategories).map(([key, name]) => (
                  <SelectItem key={key} value={key}>{name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Selection Controls */}
          <div className="flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              {Object.keys(selectedProducts).length} products selected
            </div>
            <div className="space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleSelectAll(filteredProducts)}
              >
                Select All Filtered
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleUnselectAll(filteredProducts)}
              >
                Clear Selection
              </Button>
            </div>
          </div>

          {/* Product List */}
          {isLoading ? (
            <div className="text-center py-10">
              <p>Loading products...</p>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-10">
              <PackageSearch className="h-10 w-10 mx-auto text-muted-foreground" />
              <p className="mt-2">No products match your search</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 pt-2">
              {filteredProducts.map(product => (
                <Card 
                  key={product.id} 
                  className={`
                    cursor-pointer border transition-colors
                    ${selectedProducts[product.id] ? 'border-primary bg-primary/5' : ''}
                  `}
                  onClick={() => toggleProductSelection(product.id)}
                >
                  <CardContent className="p-3 flex items-start space-x-3">
                    <Checkbox 
                      checked={!!selectedProducts[product.id]} 
                      onCheckedChange={() => toggleProductSelection(product.id)}
                      onClick={(e) => e.stopPropagation()}
                      className="mt-1"
                    />
                    <div className="flex-grow">
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-muted-foreground truncate max-w-md">
                        {product.description || 'No description'}
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <Badge variant="secondary">{getCategoryName(product.system_category)}</Badge>
                        <span className="font-medium">${product.unit_cost.toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleAddSelected} disabled={Object.keys(selectedProducts).length === 0}>
            Add Selected Products
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProductSelector;
