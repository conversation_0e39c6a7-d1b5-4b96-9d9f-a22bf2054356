import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Calculator, FileText, Download } from 'lucide-react';
import { CostStructure, CostSummary } from '@/models/CostStructure';
import { saveQuote } from '@/services/quoteService';
import { generateQuotePDF } from '@/utils/quotePdfGenerator';
import { addDays, format } from 'date-fns';

interface GenerateQuoteDialogProps {
  costStructure: CostStructure;
  costSummary: CostSummary;
  onQuoteGenerated?: (quoteId: string) => void;
}

const GenerateQuoteDialog: React.FC<GenerateQuoteDialogProps> = ({
  costStructure,
  costSummary,
  onQuoteGenerated
}) => {
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [clientName, setClientName] = useState('');
  const [clientEmail, setClientEmail] = useState('');
  const [projectType, setProjectType] = useState('');
  const [notes, setNotes] = useState('');
  const [validityPeriod, setValidityPeriod] = useState('30');
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleGenerateQuote = async () => {
    if (!title.trim() || !clientName.trim()) {
      toast({
        title: "Required Fields Missing",
        description: "Please enter a quote title and client name",
        variant: "destructive",
      });
      return;
    }

    if (!costSummary) {
      toast({
        title: "Cost Summary Missing",
        description: "Please ensure the cost structure has been calculated",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const validUntil = validityPeriod ? addDays(new Date(), parseInt(validityPeriod)) : undefined;

      const quoteId = await saveQuote(
        title.trim(),
        clientName.trim(),
        clientEmail.trim(),
        projectType.trim(),
        costStructure,
        costSummary,
        notes.trim() || undefined,
        validUntil
      );

      if (quoteId) {
        // Get the saved quote to generate PDF
        const quote = {
          id: quoteId,
          title: title.trim(),
          clientName: clientName.trim(),
          clientEmail: clientEmail.trim(),
          projectType: projectType.trim(),
          costStructure,
          costSummary,
          status: 'draft' as const,
          notes: notes.trim() || undefined,
          validUntil,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Generate PDF
        generateQuotePDF(quote);

        toast({
          title: "Quote Generated Successfully",
          description: "Quote has been saved and PDF downloaded",
        });

        if (onQuoteGenerated) {
          onQuoteGenerated(quoteId);
        }

        setOpen(false);
        // Reset form
        setTitle('');
        setClientName('');
        setClientEmail('');
        setProjectType('');
        setNotes('');
        setValidityPeriod('30');
      } else {
        throw new Error("Failed to save quote");
      }
    } catch (error) {
      console.error("Error generating quote:", error);
      toast({
        title: "Error",
        description: "Failed to generate quote. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-[#37372f] hover:bg-[#4a4a3f] text-white gap-2">
          <Calculator className="h-4 w-4" />
          Generate Quote
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Generate Professional Quote
          </DialogTitle>
          <DialogDescription>
            Create a professional quote document with your cost structure and client information.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Quote Title *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="e.g., Home Automation System Quote"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="clientName">Client Name *</Label>
              <Input
                id="clientName"
                value={clientName}
                onChange={(e) => setClientName(e.target.value)}
                placeholder="Client full name"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="clientEmail">Client Email</Label>
              <Input
                id="clientEmail"
                type="email"
                value={clientEmail}
                onChange={(e) => setClientEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="projectType">Project Type</Label>
              <Select value={projectType} onValueChange={setProjectType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select project type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="residential">Residential</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                  <SelectItem value="renovation">Renovation</SelectItem>
                  <SelectItem value="new-construction">New Construction</SelectItem>
                  <SelectItem value="upgrade">System Upgrade</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="validityPeriod">Quote Valid For</Label>
            <Select value={validityPeriod} onValueChange={setValidityPeriod}>
              <SelectTrigger>
                <SelectValue placeholder="Select validity period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15">15 days</SelectItem>
                <SelectItem value="30">30 days</SelectItem>
                <SelectItem value="45">45 days</SelectItem>
                <SelectItem value="60">60 days</SelectItem>
                <SelectItem value="90">90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any additional terms, conditions, or notes for the client..."
              className="min-h-[100px]"
            />
          </div>

          {/* Quote Summary Preview */}
          {costSummary && (
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">Quote Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>${costSummary.subtotal.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax:</span>
                  <span>${costSummary.tax.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
                </div>
                {costSummary.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount:</span>
                    <span>-${costSummary.discount.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t pt-1">
                  <span>Total:</span>
                  <span>${costSummary.total.toLocaleString('en-US', { minimumFractionDigits: 2 })}</span>
                </div>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleGenerateQuote}
            disabled={isGenerating || !title.trim() || !clientName.trim() || !costSummary}
            className="gap-2 bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            {isGenerating ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent"></div>
                Generating...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Generate & Download
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default GenerateQuoteDialog;
