import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { Save, BookmarkPlus } from 'lucide-react';
import { CostStructure, CostSummary } from '@/models/CostStructure';
import { saveCostStructure } from '@/services/quoteService';

interface SaveCostStructureDialogProps {
  costStructure: CostStructure;
  costSummary: CostSummary;
  onSaved?: (id: string) => void;
}

const SaveCostStructureDialog: React.FC<SaveCostStructureDialogProps> = ({
  costStructure,
  costSummary,
  onSaved
}) => {
  const [open, setOpen] = useState(false);
  const [name, setName] = useState(costStructure.name || '');
  const [description, setDescription] = useState(costStructure.description || '');
  const [isTemplate, setIsTemplate] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const handleSave = async () => {
    if (!name.trim()) {
      toast({
        title: "Name Required",
        description: "Please enter a name for the cost structure",
        variant: "destructive",
      });
      return;
    }

    if (!costSummary) {
      toast({
        title: "Cost Summary Missing",
        description: "Please ensure the cost structure has been calculated",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);

    try {
      const id = await saveCostStructure(
        name.trim(),
        description.trim(),
        costStructure,
        costSummary,
        isTemplate
      );

      if (id) {
        toast({
          title: "Success",
          description: `Cost structure saved successfully${isTemplate ? ' as template' : ''}`,
        });

        if (onSaved) {
          onSaved(id);
        }

        setOpen(false);
        setName('');
        setDescription('');
        setIsTemplate(false);
      } else {
        throw new Error("Failed to save cost structure");
      }
    } catch (error) {
      console.error("Error saving cost structure:", error);
      toast({
        title: "Error",
        description: "Failed to save cost structure. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <Save className="h-4 w-4" />
          Save Structure
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookmarkPlus className="h-5 w-5" />
            Save Cost Structure
          </DialogTitle>
          <DialogDescription>
            Save this cost structure for future use or as a template for similar projects.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter a name for this cost structure"
              className="w-full"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Optional description or notes about this cost structure"
              className="w-full min-h-[80px]"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="template"
              checked={isTemplate}
              onCheckedChange={(checked) => setIsTemplate(checked as boolean)}
            />
            <Label htmlFor="template" className="text-sm">
              Save as template for reuse
            </Label>
          </div>
          {isTemplate && (
            <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
              <strong>Template Mode:</strong> This cost structure will be saved as a reusable template
              that can be loaded and modified for future projects.
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !name.trim() || !costSummary}
            className="gap-2 bg-primary hover:bg-primary/90 text-primary-foreground"
          >
            {isSaving ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Save Structure
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SaveCostStructureDialog;
