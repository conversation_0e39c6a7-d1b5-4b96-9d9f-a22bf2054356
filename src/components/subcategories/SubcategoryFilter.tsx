import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSubcategoryPreferences } from '@/hooks/useSubcategoryPreferences';

interface Subcategory {
  id: string;
  name: string;
  systemCategory: string;
}

interface SubcategoryFilterProps {
  systemCategory: string;
  selectedSubcategory: string | null;
  onSubcategoryChange: (subcategory: string | null) => void;
}

const SubcategoryFilter: React.FC<SubcategoryFilterProps> = ({
  systemCategory,
  selectedSubcategory,
  onSubcategoryChange
}) => {
  const { getEnabledSubcategoriesForSystem } = useSubcategoryPreferences();

  // Get enabled subcategories from preferences
  const subcategories: Subcategory[] = React.useMemo(() => {
    if (!systemCategory) return [];
    return getEnabledSubcategoriesForSystem(systemCategory);
  }, [systemCategory, getEnabledSubcategoriesForSystem]);

  if (!systemCategory || subcategories.length === 0) {
    return null;
  }

  return (
    <Select
      value={selectedSubcategory || 'all'}
      onValueChange={(value) => onSubcategoryChange(value === 'all' ? null : value)}
    >
      <SelectTrigger className="w-[200px]">
        <SelectValue placeholder="All Subcategories" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Subcategories</SelectItem>
        {subcategories.map(subcategory => (
          <SelectItem key={subcategory.id} value={subcategory.name}>
            {subcategory.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default SubcategoryFilter;
