import React, { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Check, ChevronDown, X, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSubcategoryPreferences } from '@/hooks/useSubcategoryPreferences';

interface Subcategory {
  id: string;
  name: string;
  systemCategory: string;
}

interface SubcategorySelectorProps {
  systemCategory: string;
  selectedSubcategories: string[];
  onChange: (subcategories: string[]) => void;
  className?: string;
  placeholder?: string;
  allowCreate?: boolean;
}

const SubcategorySelector: React.FC<SubcategorySelectorProps> = ({
  systemCategory,
  selectedSubcategories,
  onChange,
  className,
  placeholder = "Select subcategories...",
  allowCreate = false
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const { getEnabledSubcategoriesForSystem } = useSubcategoryPreferences();

  // Get available subcategories from preferences (enabled defaults + custom)
  const availableSubcategories: Subcategory[] = React.useMemo(() => {
    if (!systemCategory) return [];
    return getEnabledSubcategoriesForSystem(systemCategory);
  }, [systemCategory, getEnabledSubcategoriesForSystem]);

  const handleSubcategoryToggle = (subcategoryName: string) => {
    const newSubcategories = selectedSubcategories.includes(subcategoryName)
      ? selectedSubcategories.filter(name => name !== subcategoryName)
      : [...selectedSubcategories, subcategoryName];

    onChange(newSubcategories);
  };

  const handleRemoveSubcategory = (subcategoryName: string) => {
    const newSubcategories = selectedSubcategories.filter(name => name !== subcategoryName);
    onChange(newSubcategories);
  };



  const filteredSubcategories = availableSubcategories.filter(subcategory =>
    subcategory.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  return (
    <div className={cn("space-y-2", className)}>
      <Label>Subcategories</Label>

      {/* Selected Subcategories Display */}
      {selectedSubcategories.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {selectedSubcategories.map(subcategoryName => (
            <Badge key={subcategoryName} variant="secondary" className="flex items-center gap-1 pr-1">
              {subcategoryName}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handleRemoveSubcategory(subcategoryName)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      {/* Subcategory Selector */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedSubcategories.length > 0
              ? `${selectedSubcategories.length} subcategory(ies) selected`
              : placeholder}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search subcategories..."
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandEmpty>
              <div className="p-2 text-center">
                <p className="text-sm text-muted-foreground">
                  No subcategories found.
                </p>
              </div>
            </CommandEmpty>
            <CommandGroup>
              {filteredSubcategories.map(subcategory => (
                <CommandItem
                  key={subcategory.id}
                  value={subcategory.name}
                  onSelect={() => handleSubcategoryToggle(subcategory.name)}
                  className="flex items-center gap-2"
                >
                  <Checkbox
                    checked={selectedSubcategories.includes(subcategory.name)}
                    onChange={() => {}} // Handled by onSelect
                  />
                  <span>{subcategory.name}</span>
                  {selectedSubcategories.includes(subcategory.name) && (
                    <Check className="ml-auto h-4 w-4" />
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Quick Selection for Common Subcategories */}
      {availableSubcategories.length > 0 && (
        <div className="space-y-2">
          <Label className="text-xs text-muted-foreground">Quick Select:</Label>
          <div className="flex flex-wrap gap-1">
            {availableSubcategories.slice(0, 6).map(subcategory => (
              <Button
                key={subcategory.id}
                type="button"
                variant={selectedSubcategories.includes(subcategory.name) ? "default" : "outline"}
                size="sm"
                className="text-xs h-6"
                onClick={() => handleSubcategoryToggle(subcategory.name)}
              >
                {subcategory.name}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SubcategorySelector;
