import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Edit2, Trash2, GripVertical, Save, X, Star, Lock } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import SubcategoryManagementPage from './SubcategoryManagementPage';

interface SubcategoryManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubcategoriesUpdated?: () => void;
}

const SubcategoryManager: React.FC<SubcategoryManagerProps> = ({
  open,
  onOpenChange,
  onSubcategoriesUpdated
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Subcategory Management</DialogTitle>
          <DialogDescription>
            Manage default and custom subcategories for product organization.
            Toggle subcategories on/off and create custom ones for your specific needs.
          </DialogDescription>
        </DialogHeader>

        <SubcategoryManagementPage />

        <div className="flex justify-end pt-4">
          <Button onClick={() => {
            onOpenChange(false);
            onSubcategoriesUpdated?.();
          }}>
            Done
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SubcategoryManager;
