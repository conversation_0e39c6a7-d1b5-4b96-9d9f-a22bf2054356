import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Plus,
  Settings,
  Star,
  Edit,
  Trash2,
  RotateCcw,
  Save,
  X,
  Package,
  Filter
} from 'lucide-react';
import { systemCategories } from '@/hooks/usePackageManagement';
import { useSubcategoryPreferences, type CustomSubcategory } from '@/hooks/useSubcategoryPreferences';
import { toast } from 'sonner';

interface SubcategoryManagementPageProps {
  isStandalone?: boolean;
}

const SubcategoryManagementPage: React.FC<SubcategoryManagementPageProps> = ({
  isStandalone = false
}) => {
  const {
    preferences,
    isLoading,
    toggleDefaultSubcategory,
    addCustomSubcategory,
    updateCustomSubcategory,
    deleteCustomSubcategory,
    getAllSubcategoriesForSystem,
    resetToDefaults
  } = useSubcategoryPreferences();

  const [selectedSystemCategory, setSelectedSystemCategory] = useState<string>('lighting');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingSubcategory, setEditingSubcategory] = useState<string | null>(null);
  const [newSubcategory, setNewSubcategory] = useState({
    name: '',
    systemCategory: 'lighting',
    order: 1,
    description: '',
    isEnabled: true
  });

  const handleAddSubcategory = () => {
    if (!newSubcategory.name.trim()) {
      toast.error('Please enter a subcategory name');
      return;
    }

    // Check if name already exists in this system category
    const existingSubcategories = getAllSubcategoriesForSystem(newSubcategory.systemCategory);
    const nameExists = existingSubcategories.some(
      sub => sub.name.toLowerCase() === newSubcategory.name.trim().toLowerCase()
    );

    if (nameExists) {
      toast.error('A subcategory with this name already exists in this system category');
      return;
    }

    // Calculate next order
    const maxOrder = Math.max(
      ...existingSubcategories.map(sub => sub.order),
      0
    );

    addCustomSubcategory({
      name: newSubcategory.name.trim(),
      systemCategory: newSubcategory.systemCategory,
      order: maxOrder + 1,
      description: newSubcategory.description.trim() || undefined,
      isEnabled: newSubcategory.isEnabled
    });

    // Reset form
    setNewSubcategory({
      name: '',
      systemCategory: 'lighting',
      order: 1,
      description: '',
      isEnabled: true
    });
    setIsAddDialogOpen(false);
    toast.success('Custom subcategory added successfully');
  };

  const handleToggleSubcategory = (subcategory: any) => {
    if (subcategory.isDefault) {
      toggleDefaultSubcategory(subcategory.systemCategory, subcategory.name);
    } else {
      updateCustomSubcategory(subcategory.id, { isEnabled: !subcategory.isEnabled });
    }
  };

  const handleDeleteCustomSubcategory = (id: string, name: string) => {
    if (confirm(`Are you sure you want to delete the custom subcategory "${name}"?`)) {
      deleteCustomSubcategory(id);
      toast.success('Custom subcategory deleted successfully');
    }
  };

  const handleResetToDefaults = () => {
    if (confirm('Are you sure you want to reset all subcategory preferences to defaults? This will remove all custom subcategories and enable all default ones.')) {
      resetToDefaults();
      toast.success('Subcategory preferences reset to defaults');
    }
  };

  const getCategoryLabel = (category: string) => {
    return systemCategories[category] || category;
  };

  const getSubcategoriesStats = () => {
    const stats = {
      total: 0,
      enabled: 0,
      custom: 0
    };

    Object.keys(systemCategories).forEach(systemKey => {
      const subcategories = getAllSubcategoriesForSystem(systemKey);
      stats.total += subcategories.length;
      stats.enabled += subcategories.filter(sub => sub.isEnabled).length;
      stats.custom += subcategories.filter(sub => sub.isCustom).length;
    });

    return stats;
  };

  const stats = getSubcategoriesStats();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading subcategory preferences...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      {isStandalone && (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Subcategory Management</h1>
            <p className="text-muted-foreground">
              Manage default and custom subcategories for product organization
            </p>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Subcategories</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                <Filter className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Enabled</p>
                <p className="text-2xl font-bold">{stats.enabled}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                <Plus className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Custom</p>
                <p className="text-2xl font-bold">{stats.custom}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-amber-100 flex items-center justify-center">
                <Star className="h-5 w-5 text-amber-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Default</p>
                <p className="text-2xl font-bold">{stats.total - stats.custom}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Custom Subcategory
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Custom Subcategory</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={newSubcategory.name}
                      onChange={(e) => setNewSubcategory(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter subcategory name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="system-category">System Category</Label>
                    <Select
                      value={newSubcategory.systemCategory}
                      onValueChange={(value) => setNewSubcategory(prev => ({ ...prev, systemCategory: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(systemCategories).map(([key, label]) => (
                          <SelectItem key={key} value={key}>{label}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={newSubcategory.description}
                    onChange={(e) => setNewSubcategory(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter description"
                    rows={3}
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="enabled"
                    checked={newSubcategory.isEnabled}
                    onCheckedChange={(checked) => setNewSubcategory(prev => ({ ...prev, isEnabled: checked }))}
                  />
                  <Label htmlFor="enabled">Enable immediately</Label>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddSubcategory}>
                    Add Subcategory
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        <Button variant="outline" onClick={handleResetToDefaults}>
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </div>

      {/* Subcategories by System Category */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Subcategories by System Category</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedSystemCategory} onValueChange={setSelectedSystemCategory}>
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
              {Object.entries(systemCategories).map(([key, label]) => (
                <TabsTrigger key={key} value={key} className="text-xs">
                  {label}
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.keys(systemCategories).map(systemKey => {
              const subcategories = getAllSubcategoriesForSystem(systemKey);

              return (
                <TabsContent key={systemKey} value={systemKey} className="mt-4">
                  <div className="space-y-3">
                    {subcategories.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-sm text-muted-foreground">
                          No subcategories available for {getCategoryLabel(systemKey)}.
                        </p>
                      </div>
                    ) : (
                      subcategories.map(subcategory => (
                        <div
                          key={subcategory.id}
                          className={`flex items-center justify-between p-4 border rounded-lg transition-colors ${
                            subcategory.isEnabled ? 'bg-background' : 'bg-muted/50'
                          }`}
                        >
                          <div className="flex items-center gap-3 flex-1">
                            <Switch
                              checked={subcategory.isEnabled}
                              onCheckedChange={() => handleToggleSubcategory(subcategory)}
                            />

                            <div className="flex items-center gap-2">
                              {subcategory.isDefault ? (
                                <Star className="h-4 w-4 text-amber-500" title="Default Subcategory" />
                              ) : (
                                <Plus className="h-4 w-4 text-purple-500" title="Custom Subcategory" />
                              )}

                              <div>
                                <div className="flex items-center gap-2">
                                  <span className={`font-medium ${!subcategory.isEnabled ? 'text-muted-foreground' : ''}`}>
                                    {subcategory.name}
                                  </span>

                                  <Badge
                                    variant={subcategory.isDefault ? "outline" : "secondary"}
                                    className={`text-xs ${
                                      subcategory.isDefault
                                        ? 'border-amber-200 text-amber-700'
                                        : 'border-purple-200 text-purple-700'
                                    }`}
                                  >
                                    {subcategory.isDefault ? 'Default' : 'Custom'}
                                  </Badge>

                                  {!subcategory.isEnabled && (
                                    <Badge variant="secondary" className="text-xs">
                                      Disabled
                                    </Badge>
                                  )}
                                </div>

                                {subcategory.description && (
                                  <p className={`text-xs mt-1 ${!subcategory.isEnabled ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                                    {subcategory.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Actions for custom subcategories */}
                          {subcategory.isCustom && (
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setEditingSubcategory(subcategory.id)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => handleDeleteCustomSubcategory(subcategory.id, subcategory.name)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </TabsContent>
              );
            })}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default SubcategoryManagementPage;
