
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Lightbulb, PanelTop, Speaker, Monitor, Wifi, ShieldCheck, Radio } from 'lucide-react';
import { SYSTEM_SUBCATEGORIES } from '../steps/SystemSelectionStep';

interface ProductSubcategoryMappingProps {
  systemCategory: string;
  selectedSubcategories: string[];
  onChange: (subcategories: string[]) => void;
}

interface SubcategoryInfo {
  name: string;
  enabled: boolean;
}

export const ProductSubcategoryMapping: React.FC<ProductSubcategoryMappingProps> = ({ 
  systemCategory, 
  selectedSubcategories,
  onChange 
}) => {
  const [availableSubcategories, setAvailableSubcategories] = useState<Record<string, SubcategoryInfo>>({});

  // Initialize available subcategories based on the system category
  useEffect(() => {
    if (systemCategory && SYSTEM_SUBCATEGORIES[systemCategory]) {
      const subcategories = SYSTEM_SUBCATEGORIES[systemCategory];
      
      // Mark the subcategories that are already selected
      const mappedSubcategories = Object.entries(subcategories).reduce((acc: Record<string, SubcategoryInfo>, [key, value]) => {
        const subcategoryKey = `${systemCategory}_${key}`;
        if (value && typeof value === 'object' && 'name' in value) {
          acc[key] = { 
            name: value.name as string,
            enabled: selectedSubcategories.includes(subcategoryKey) 
          };
        }
        return acc;
      }, {});
      
      setAvailableSubcategories(mappedSubcategories);
    }
  }, [systemCategory, selectedSubcategories]);

  const handleSubcategoryChange = (subcategory: string, checked: boolean) => {
    const subcategoryKey = `${systemCategory}_${subcategory}`;
    
    let newSubcategories = [...selectedSubcategories];
    
    if (checked) {
      if (!newSubcategories.includes(subcategoryKey)) {
        newSubcategories.push(subcategoryKey);
      }
    } else {
      newSubcategories = newSubcategories.filter(item => item !== subcategoryKey);
    }
    
    onChange(newSubcategories);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'lighting': return <Lightbulb className="h-5 w-5 text-amber-500" />;
      case 'shades': return <PanelTop className="h-5 w-5 text-indigo-500" />;
      case 'audio': return <Speaker className="h-5 w-5 text-emerald-500" />;
      case 'video': return <Monitor className="h-5 w-5 text-blue-500" />;
      case 'network': return <Wifi className="h-5 w-5 text-sky-500" />;
      case 'security': return <ShieldCheck className="h-5 w-5 text-rose-500" />;
      case 'controlSystems': return <Radio className="h-5 w-5 text-purple-500" />;
      default: return null;
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'lighting': return 'Lighting';
      case 'shades': return 'Shades';
      case 'audio': return 'Audio';
      case 'video': return 'Video';
      case 'network': return 'Network';
      case 'security': return 'Security';
      case 'controlSystems': return 'Control';
      default: return category;
    }
  };

  // If no system category is selected or none available
  if (!systemCategory || !SYSTEM_SUBCATEGORIES[systemCategory]) {
    return (
      <div className="text-muted-foreground text-sm p-4 text-center">
        Select a system category to map subcategories
      </div>
    );
  }

  return (
    <Card className="border">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          {getCategoryIcon(systemCategory)}
          <CardTitle className="text-base">{getCategoryLabel(systemCategory)} Subcategories</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {Object.entries(availableSubcategories).map(([key, { name, enabled }]) => (
            <div key={key} className="flex items-center space-x-2">
              <Checkbox 
                id={`subcategory-${systemCategory}-${key}`}
                checked={enabled}
                onCheckedChange={(checked) => {
                  handleSubcategoryChange(
                    key,
                    checked === 'indeterminate' ? false : !!checked
                  );
                }}
              />
              <Label 
                htmlFor={`subcategory-${systemCategory}-${key}`} 
                className="text-sm font-medium leading-none"
              >
                {name || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </Label>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProductSubcategoryMapping;
