
import React from 'react';
import { Badge } from '@/components/ui/badge';

interface CategorySubcategoriesListProps {
  subcategories: string[];
  onClick?: (subcategory: string) => void;
}

const CategorySubcategoriesList: React.FC<CategorySubcategoriesListProps> = ({
  subcategories,
  onClick
}) => {
  if (!subcategories || subcategories.length === 0) {
    return <p className="text-sm text-muted-foreground">No subcategories</p>;
  }

  return (
    <div className="flex flex-wrap gap-1 my-2">
      {subcategories.map((subcategory) => (
        <Badge 
          key={subcategory} 
          variant="secondary" 
          className={`mr-1 mb-1 ${onClick ? 'cursor-pointer hover:bg-secondary/80' : ''}`}
          onClick={onClick ? () => onClick(subcategory) : undefined}
        >
          {subcategory}
        </Badge>
      ))}
    </div>
  );
};

export default CategorySubcategoriesList;
