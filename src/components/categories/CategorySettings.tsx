
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

const CategorySettings: React.FC = () => {
  const [autoTaggingEnabled, setAutoTaggingEnabled] = useState(false);
  const [suggestCategoriesEnabled, setSuggestCategoriesEnabled] = useState(true);
  const [autoOrganizeEnabled, setAutoOrganizeEnabled] = useState(false);
  const { toast } = useToast();

  const handleSaveSettings = () => {
    // In a real implementation, this would save to the database
    toast({
      title: "Settings Saved",
      description: "Your category settings have been updated"
    });
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Category Settings</h2>
      
      <Card>
        <CardHeader>
          <CardTitle>Automation Settings</CardTitle>
          <CardDescription>
            Configure how categories are handled in the product catalog
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between space-x-2">
            <div className="space-y-0.5">
              <Label htmlFor="auto-tagging">Auto-tagging</Label>
              <p className="text-sm text-muted-foreground">
                Automatically suggest categories for new products based on names and descriptions
              </p>
            </div>
            <Switch
              id="auto-tagging"
              checked={autoTaggingEnabled}
              onCheckedChange={setAutoTaggingEnabled}
            />
          </div>
          
          <div className="flex items-center justify-between space-x-2">
            <div className="space-y-0.5">
              <Label htmlFor="suggest-categories">Suggest Categories</Label>
              <p className="text-sm text-muted-foreground">
                Show category suggestions when adding or editing products
              </p>
            </div>
            <Switch
              id="suggest-categories"
              checked={suggestCategoriesEnabled}
              onCheckedChange={setSuggestCategoriesEnabled}
            />
          </div>
          
          <div className="flex items-center justify-between space-x-2">
            <div className="space-y-0.5">
              <Label htmlFor="auto-organize">Auto-organize</Label>
              <p className="text-sm text-muted-foreground">
                Automatically organize products into categories when importing
              </p>
            </div>
            <Switch
              id="auto-organize"
              checked={autoOrganizeEnabled}
              onCheckedChange={setAutoOrganizeEnabled}
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSaveSettings}>Save Settings</Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Category Management</CardTitle>
          <CardDescription>
            Options for managing your product categories
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm">
            Categories help you organize your products and improve the browsing experience.
            You can create custom categories and subcategories to fit your specific inventory needs.
          </p>
          
          <div className="p-4 rounded-md bg-muted mt-4">
            <h4 className="font-medium mb-2">Tips for effective categorization:</h4>
            <ul className="list-disc pl-5 space-y-1 text-sm">
              <li>Use clear, descriptive category names</li>
              <li>Keep your category structure consistent</li>
              <li>Limit subcategories to maintain simplicity</li>
              <li>Consider using tags for cross-category attributes</li>
              <li>Regularly review and update your category structure</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CategorySettings;
