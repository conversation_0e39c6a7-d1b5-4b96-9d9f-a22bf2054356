
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { systemCategories } from '@/hooks/usePackageManagement'; // Fixed import
import CategorySubcategoryEditor from './CategorySubcategoryEditor';
import { SYSTEM_SUBCATEGORIES } from '@/components/steps/SystemSelectionStep';
import CategorySubcategoriesList from './CategorySubcategoriesList';

// Define the interface for subcategory data
interface SubcategoryData {
  name: string;
  description?: string;
  [key: string]: any; // Add this to allow for other properties
}

export interface CategoryItem {
  id: string;
  name: string;
  description: string;
  systemCategory: string;
  subcategories: string[];
  createdAt: string;
  updatedAt: string;
}

const CategoryManager: React.FC = () => {
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<CategoryItem | null>(null);
  const { toast } = useToast();

  // Load categories on mount
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching categories:', error);
        toast({
          title: "Failed to load categories",
          description: error.message,
          variant: "destructive"
        });
        return;
      }

      // Map database fields to our format
      const mappedCategories: CategoryItem[] = data.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description || '',
        systemCategory: item.system_category,
        subcategories: item.subcategories || [],
        createdAt: item.created_at,
        updatedAt: item.updated_at
      }));

      setCategories(mappedCategories);
      console.log(`Loaded ${mappedCategories.length} categories`);
    } catch (error) {
      console.error('Error in fetch process:', error);
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new empty category
  const handleCreateCategory = () => {
    const newCategory: CategoryItem = {
      id: crypto.randomUUID(),
      name: '',
      description: '',
      systemCategory: 'lighting',
      subcategories: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setCurrentCategory(newCategory);
    setIsCreateDialogOpen(true);
  };

  // Edit an existing category
  const handleEditCategory = (category: CategoryItem) => {
    setCurrentCategory({ ...category });
    setIsEditDialogOpen(true);
  };

  // Confirm deletion of a category
  const handleDeletePrompt = (category: CategoryItem) => {
    setCurrentCategory(category);
    setIsDeleteDialogOpen(true);
  };

  // Update category field
  const handleUpdateCategory = <T extends keyof CategoryItem>(
    field: T,
    value: CategoryItem[T]
  ) => {
    setCurrentCategory(prev => {
      if (!prev) return null;
      return { ...prev, [field]: value };
    });
  };

  // Save a new category
  const handleSaveNewCategory = async () => {
    if (!currentCategory) return;
    if (!currentCategory.name) {
      toast({
        title: "Name Required",
        description: "Please provide a name for the category",
        variant: "destructive"
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('product_categories')
        .insert({
          id: currentCategory.id,
          name: currentCategory.name,
          description: currentCategory.description,
          system_category: currentCategory.systemCategory,
          subcategories: currentCategory.subcategories
        })
        .select();

      if (error) {
        console.error('Error saving category:', error);
        toast({
          title: "Save Failed",
          description: error.message,
          variant: "destructive"
        });
        return;
      }

      // Add the new category to the state
      if (data && data.length > 0) {
        const savedCategory: CategoryItem = {
          id: data[0].id,
          name: data[0].name,
          description: data[0].description || '',
          systemCategory: data[0].system_category,
          subcategories: data[0].subcategories || [],
          createdAt: data[0].created_at,
          updatedAt: data[0].updated_at
        };

        setCategories(prev => [...prev, savedCategory]);
        
        toast({
          title: "Category Created",
          description: `${savedCategory.name} has been created successfully`
        });
      }

      setCurrentCategory(null);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: "Save Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  // Update an existing category
  const handleUpdateExistingCategory = async () => {
    if (!currentCategory) return;
    if (!currentCategory.name) {
      toast({
        title: "Name Required",
        description: "Please provide a name for the category",
        variant: "destructive"
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('product_categories')
        .update({
          name: currentCategory.name,
          description: currentCategory.description,
          system_category: currentCategory.systemCategory,
          subcategories: currentCategory.subcategories,
          updated_at: new Date().toISOString()
        })
        .eq('id', currentCategory.id);

      if (error) {
        console.error('Error updating category:', error);
        toast({
          title: "Update Failed",
          description: error.message,
          variant: "destructive"
        });
        return;
      }

      // Update the category in the state
      setCategories(prev => 
        prev.map(cat => cat.id === currentCategory.id ? currentCategory : cat)
      );

      toast({
        title: "Category Updated",
        description: `${currentCategory.name} has been updated successfully`
      });
      
      setCurrentCategory(null);
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating category:', error);
      toast({
        title: "Update Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  // Delete a category
  const handleDeleteCategory = async () => {
    if (!currentCategory) return;

    try {
      const { error } = await supabase
        .from('product_categories')
        .delete()
        .eq('id', currentCategory.id);

      if (error) {
        console.error('Error deleting category:', error);
        toast({
          title: "Delete Failed",
          description: error.message,
          variant: "destructive"
        });
        return;
      }

      // Remove the deleted category from state
      setCategories(prev => prev.filter(cat => cat.id !== currentCategory.id));

      toast({
        title: "Category Deleted",
        description: `${currentCategory.name} has been deleted successfully`
      });
      
      setCurrentCategory(null);
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: "Delete Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };
  
  // Import subcategories from the walkthrough System Selection
  const handleImportSubcategories = async () => {
    try {
      // For each system category, create or update a product category
      for (const [systemKey, subcategoriesObj] of Object.entries(SYSTEM_SUBCATEGORIES)) {
        // First check if this system category exists
        const existingCategoryIndex = categories.findIndex(
          cat => cat.systemCategory === systemKey
        );
        
        // Get subcategory names from the walkthrough subcategories
        const subcategoryNames = Object.entries(subcategoriesObj)
          .map(([key, data]) => {
            // Explicitly cast to the SubcategoryData type
            const subcategoryData = data as SubcategoryData;
            return subcategoryData.name;
          });
        
        if (existingCategoryIndex >= 0) {
          // Update existing category with subcategories
          const updatedCategory = { 
            ...categories[existingCategoryIndex],
            subcategories: subcategoryNames
          };
          
          await supabase
            .from('product_categories')
            .update({
              subcategories: subcategoryNames,
              updated_at: new Date().toISOString()
            })
            .eq('id', updatedCategory.id);
            
          // Update in local state
          setCategories(prev => prev.map(
            cat => cat.id === updatedCategory.id ? updatedCategory : cat
          ));
        } else {
          // Create new category with subcategories
          const categoryName = systemCategories[systemKey as keyof typeof systemCategories] || 
            systemKey.replace(/([A-Z])/g, ' $1').trim();
            
          const newCategory: CategoryItem = {
            id: crypto.randomUUID(),
            name: categoryName,
            description: `${categoryName} products and components`,
            systemCategory: systemKey,
            subcategories: subcategoryNames,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          const { data, error } = await supabase
            .from('product_categories')
            .insert({
              id: newCategory.id,
              name: newCategory.name,
              description: newCategory.description,
              system_category: newCategory.systemCategory,
              subcategories: newCategory.subcategories
            })
            .select();
            
          if (!error && data && data.length > 0) {
            // Add to local state
            setCategories(prev => [...prev, newCategory]);
          }
        }
      }
      
      toast({
        title: "Subcategories Imported",
        description: "Successfully imported subcategories from walkthrough system"
      });
      
    } catch (error) {
      console.error('Error importing subcategories:', error);
      toast({
        title: "Import Failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  // Render category card
  const renderCategoryCard = (category: CategoryItem) => (
    <Card key={category.id} className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{category.name}</CardTitle>
            <CardDescription className="mt-1">
              {category.description || 'No description provided'}
            </CardDescription>
          </div>
          <Badge variant="outline" className="capitalize">
            {systemCategories[category.systemCategory as keyof typeof systemCategories] || category.systemCategory}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <CategorySubcategoriesList subcategories={category.subcategories} />
      </CardContent>
      <CardFooter className="flex justify-end gap-2 pt-0">
        <Button variant="outline" size="sm" onClick={() => handleEditCategory(category)}>
          <Edit className="h-4 w-4 mr-1" /> Edit
        </Button>
        <Button variant="outline" size="sm" className="text-destructive" onClick={() => handleDeletePrompt(category)}>
          <Trash2 className="h-4 w-4 mr-1" /> Delete
        </Button>
      </CardFooter>
    </Card>
  );

  // Render create/edit dialog
  const renderCategoryDialog = (isCreate: boolean) => {
    const dialogOpen = isCreate ? isCreateDialogOpen : isEditDialogOpen;
    const setDialogOpen = isCreate ? setIsCreateDialogOpen : setIsEditDialogOpen;
    const handleSave = isCreate ? handleSaveNewCategory : handleUpdateExistingCategory;
    
    return (
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{isCreate ? "Create Category" : "Edit Category"}</DialogTitle>
            <DialogDescription>
              {isCreate 
                ? "Add a new category to organize your products" 
                : "Update the details of this category"
              }
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="category-name">Category Name</Label>
              <Input
                id="category-name"
                placeholder="Enter category name"
                value={currentCategory?.name || ''}
                onChange={(e) => handleUpdateCategory('name', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category-description">Description</Label>
              <Textarea
                id="category-description"
                placeholder="Enter category description"
                value={currentCategory?.description || ''}
                onChange={(e) => handleUpdateCategory('description', e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="system-category">System Category</Label>
              <Select
                value={currentCategory?.systemCategory || 'lighting'}
                onValueChange={(value) => handleUpdateCategory('systemCategory', value)}
              >
                <SelectTrigger id="system-category">
                  <SelectValue placeholder="Select system category" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(systemCategories).map(([key, label]) => (
                    <SelectItem key={key} value={key}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {currentCategory && (
              <CategorySubcategoryEditor 
                subcategories={currentCategory.subcategories}
                onChange={(subcategories) => handleUpdateCategory('subcategories', subcategories)}
              />
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSave}>{isCreate ? "Create" : "Update"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // Render delete confirmation dialog
  const renderDeleteDialog = () => (
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the category "{currentCategory?.name}".
            This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleDeleteCategory} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Product Categories</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleImportSubcategories}>
            Import Walkthrough Subcategories
          </Button>
          <Button onClick={handleCreateCategory}>
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        </div>
      </div>
      
      {isLoading ? (
        <div className="text-center py-10">
          <p>Loading categories...</p>
        </div>
      ) : categories.length === 0 ? (
        <div className="text-center py-10 border rounded-lg bg-muted/20">
          <h3 className="text-lg font-medium mb-2">No categories found</h3>
          <p className="text-muted-foreground mb-4">Create your first category to start organizing products.</p>
          <Button onClick={handleCreateCategory}>
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {categories.map(renderCategoryCard)}
        </div>
      )}
      
      {/* Dialogs */}
      {renderCategoryDialog(true)}
      {renderCategoryDialog(false)}
      {renderDeleteDialog()}
    </div>
  );
};

export default CategoryManager;
