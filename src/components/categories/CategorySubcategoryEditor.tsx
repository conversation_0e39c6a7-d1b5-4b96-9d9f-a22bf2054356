
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Plus, X } from 'lucide-react';

interface CategorySubcategoryEditorProps {
  subcategories: string[];
  onChange: (subcategories: string[]) => void;
}

const CategorySubcategoryEditor: React.FC<CategorySubcategoryEditorProps> = ({
  subcategories,
  onChange
}) => {
  const [newSubcategory, setNewSubcategory] = useState('');

  const handleAddSubcategory = () => {
    if (!newSubcategory.trim()) return;
    
    // Check if subcategory already exists
    if (subcategories.includes(newSubcategory.trim())) {
      setNewSubcategory('');
      return;
    }
    
    // Add the new subcategory
    const updatedSubcategories = [...subcategories, newSubcategory.trim()];
    onChange(updatedSubcategories);
    setNewSubcategory('');
  };

  const handleRemoveSubcategory = (subcategory: string) => {
    const updatedSubcategories = subcategories.filter(s => s !== subcategory);
    onChange(updatedSubcategories);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSubcategory();
    }
  };

  return (
    <div className="space-y-4">
      <Label>Subcategories</Label>
      
      <div className="flex space-x-2">
        <Input
          placeholder="Add a subcategory"
          value={newSubcategory}
          onChange={(e) => setNewSubcategory(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        <Button type="button" onClick={handleAddSubcategory}>
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="flex flex-wrap gap-2 mt-2">
        {subcategories.length === 0 ? (
          <p className="text-sm text-muted-foreground">No subcategories added</p>
        ) : (
          subcategories.map((subcategory) => (
            <Badge key={subcategory} variant="secondary" className="flex items-center gap-1 pr-1">
              {subcategory}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handleRemoveSubcategory(subcategory)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))
        )}
      </div>
    </div>
  );
};

export default CategorySubcategoryEditor;
