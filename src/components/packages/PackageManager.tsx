import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Package,
  Plus,
  Search,
  Edit,
  Trash2,
  Copy,
  DollarSign,
  ShoppingCart,
  Eye,
  Filter,
  Grid3X3,
  BarChart3,
  TrendingUp,
  Layers,
  Lightbulb,
  PanelTop,
  Monitor,
  Wifi,
  ShieldCheck,
  Radio
} from 'lucide-react';
import { useCustomPackages } from '@/hooks/useCustomPackages';
import { ProductPackage } from '@/types/packageTypes';
import { PackageBuilder } from './PackageBuilder';
import { PackageViewer } from './PackageViewer';
import { PackageMigration } from './PackageMigration';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { checkMigrationStatus } from '@/utils/runPackageMigration';

// Standard system categories used in walkthroughs
const SYSTEM_CATEGORIES = [
  { key: 'lighting', label: 'Lighting System', icon: Lightbulb },
  { key: 'shades', label: 'Shading System', icon: PanelTop },
  { key: 'audio', label: 'Audio System', icon: Monitor },
  { key: 'video', label: 'Video System', icon: Monitor },
  { key: 'network', label: 'Networking System', icon: Wifi },
  { key: 'security', label: 'Surveillance System', icon: ShieldCheck },
  { key: 'controlSystems', label: 'Control System', icon: Radio },
];

// Helper functions for category display
const getCategoryIcon = (key: string) => {
  const category = SYSTEM_CATEGORIES.find(cat => cat.key === key);
  return category ? category.icon : Package;
};

const getCategoryLabel = (key: string) => {
  const category = SYSTEM_CATEGORIES.find(cat => cat.key === key);
  return category ? category.label : key;
};

export const PackageManager: React.FC = () => {
  const {
    packages,
    isLoading,
    deletePackage,
    getPackagesByCategory,
    searchPackages,
    calculatePackagePricing
  } = useCustomPackages();

  const [showBuilder, setShowBuilder] = useState(false);
  const [showViewer, setShowViewer] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [migrationCompleted, setMigrationCompleted] = useState<boolean | null>(null);

  // Check migration status on mount
  React.useEffect(() => {
    const checkMigration = async () => {
      try {
        const isCompleted = await checkMigrationStatus();
        setMigrationCompleted(isCompleted);
      } catch (error) {
        console.error('Error checking migration status:', error);
        setMigrationCompleted(false);
      }
    };
    checkMigration();
  }, []);

  // Use standard system categories instead of dynamic ones from packages
  const categories = SYSTEM_CATEGORIES.map(cat => cat.key);

  // Filter packages based on search and category
  const filteredPackages = packages.filter(pkg => {
    const matchesSearch = !searchTerm ||
      pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.items.some(item =>
        item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.product.sku.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesCategory = selectedCategory === 'all' || pkg.systemCategory === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const handleViewPackage = (pkg: ProductPackage) => {
    setSelectedPackage(pkg);
    setShowViewer(true);
  };

  const handleDeletePackage = async (packageId: string, packageName: string) => {
    if (window.confirm(`Are you sure you want to delete the package "${packageName}"? This action cannot be undone.`)) {
      await deletePackage(packageId);
    }
  };

  const handleDuplicatePackage = (pkg: ProductPackage) => {
    // TODO: Implement package duplication
    console.log('Duplicate package:', pkg.name);
  };

  const handlePackageCreated = (packageId: string) => {
    setShowBuilder(false);
    // Packages will be automatically refreshed by the hook
  };

  // Show migration component if migration is not completed
  if (migrationCompleted === false) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <Package className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold">Package Manager</h1>
            <p className="text-muted-foreground">Database setup required for package management</p>
          </div>
        </div>
        <PackageMigration />
      </div>
    );
  }

  if (isLoading || migrationCompleted === null) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Loading packages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      <div className="flex-1 flex flex-col p-6 space-y-6">
        {/* Compact Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-lg bg-primary flex items-center justify-center">
              <Layers className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Package Manager</h1>
              <p className="text-sm text-muted-foreground">Create and manage custom product packages</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Grid3X3 className="h-4 w-4" />
              View Options
            </Button>
            <Button
              onClick={() => setShowBuilder(true)}
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Create Package
            </Button>
          </div>
        </div>

        {/* Compact Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search packages by name, description, or product..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-9"
              />
            </div>
          </div>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48 h-9">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All categories</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {getCategoryLabel(category)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Compact Statistics Dashboard */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold">{packages.length}</p>
                  <p className="text-xs text-muted-foreground">Total Packages</p>
                </div>
                <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                  <Package className="h-4 w-4 text-primary-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold">
                    {packages.reduce((sum, pkg) => sum + pkg.items.length, 0)}
                  </p>
                  <p className="text-xs text-muted-foreground">Total Items</p>
                </div>
                <div className="h-8 w-8 rounded-lg bg-secondary flex items-center justify-center">
                  <ShoppingCart className="h-4 w-4 text-secondary-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold">
                    ${packages.reduce((sum, pkg) => sum + pkg.finalPrice, 0).toLocaleString('en-US', { maximumFractionDigits: 0 })}
                  </p>
                  <p className="text-xs text-muted-foreground">Total Value</p>
                </div>
                <div className="h-8 w-8 rounded-lg bg-accent flex items-center justify-center">
                  <DollarSign className="h-4 w-4 text-accent-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold">{categories.length}</p>
                  <p className="text-xs text-muted-foreground">Categories</p>
                </div>
                <div className="h-8 w-8 rounded-lg bg-muted flex items-center justify-center">
                  <Grid3X3 className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Packages Grid - Scrollable */}
        <div className="flex-1 overflow-hidden">
          {filteredPackages.length === 0 ? (
            <Card className="h-full">
              <CardContent className="h-full flex items-center justify-center">
                <div className="text-center max-w-md mx-auto">
                  <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                    <Package className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">
                    {packages.length === 0 ? 'No packages created yet' : 'No packages found'}
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    {packages.length === 0
                      ? 'Create your first custom package by combining multiple products into organized bundles'
                      : 'Try adjusting your search criteria or create a new package'
                    }
                  </p>
                  <div className="flex justify-center">
                    <Button
                      onClick={() => setShowBuilder(true)}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Create Your First Package
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="h-full overflow-y-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 pb-4">
            {filteredPackages.map(pkg => {
              const pricing = calculatePackagePricing(pkg);

              return (
                <Card
                  key={pkg.id}
                  className="group hover:shadow-md transition-all duration-200 h-fit"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                            <Package className="h-4 w-4 text-primary-foreground" />
                          </div>
                          <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors truncate">
                            {pkg.name}
                          </CardTitle>
                        </div>
                        {pkg.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2">{pkg.description}</p>
                        )}
                      </div>
                      {pkg.isDefault && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Default
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {getCategoryLabel(pkg.systemCategory)}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <ShoppingCart className="h-3 w-3" />
                        <span>{pkg.items.length} item{pkg.items.length !== 1 ? 's' : ''}</span>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {/* Compact Price Summary */}
                      <div className="bg-muted/50 rounded-lg p-3">
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>Subtotal:</span>
                            <span className="font-medium">${pricing.subtotal.toFixed(2)}</span>
                          </div>
                          {pricing.markup > 0 && (
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>Markup ({pricing.markup}%):</span>
                              <span className="font-medium">+${pricing.markupAmount.toFixed(2)}</span>
                            </div>
                          )}
                          <Separator className="my-1" />
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-semibold">Total:</span>
                            <span className="text-lg font-bold">${pricing.finalPrice.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>

                      {/* Compact Items Preview */}
                      <div>
                        <p className="text-xs font-semibold mb-2 flex items-center gap-1">
                          <Layers className="h-3 w-3" />
                          Contents:
                        </p>
                        <div className="space-y-1">
                          {pkg.items.slice(0, 2).map(item => (
                            <div key={item.id} className="flex justify-between items-center text-xs bg-muted/50 rounded px-2 py-1">
                              <span className="truncate font-medium">{item.product.name}</span>
                              <Badge variant="secondary" className="text-xs">
                                ×{item.quantity}
                              </Badge>
                            </div>
                          ))}
                          {pkg.items.length > 2 && (
                            <div className="text-xs text-muted-foreground text-center py-1 bg-muted/50 rounded">
                              +{pkg.items.length - 2} more items
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Compact Actions */}
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewPackage(pkg)}
                          className="flex-1 text-xs h-8"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDuplicatePackage(pkg)}
                          className="h-8 w-8 p-0"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeletePackage(pkg.id, pkg.name)}
                          className="text-destructive hover:bg-destructive/10 h-8 w-8 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
              </div>
            </div>
          )}
        </div>

        {/* Package Builder Modal */}
        {showBuilder && (
          <PackageBuilder
            onClose={() => setShowBuilder(false)}
            onPackageCreated={handlePackageCreated}
          />
        )}

        {/* Package Viewer Modal */}
        {showViewer && selectedPackage && (
          <PackageViewer
            package={selectedPackage}
            onClose={() => {
              setShowViewer(false);
              setSelectedPackage(null);
            }}
          />
        )}
      </div>
    </div>
  );
};
