import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Plus, Minus, X, Package, DollarSign, Calculator, Lightbulb, PanelTop, Monitor, Wifi, ShieldCheck, Radio } from 'lucide-react';
import { useCustomPackages } from '@/hooks/useCustomPackages';
import usePackageManagement, { ProductItem } from '@/hooks/usePackageManagement';
import { CreatePackageRequest, PackagePricingCalculation } from '@/types/packageTypes';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Standard system categories used in walkthroughs
const SYSTEM_CATEGORIES = [
  { key: 'lighting', label: 'Lighting System', icon: Lightbulb },
  { key: 'shades', label: 'Shading System', icon: PanelTop },
  { key: 'audio', label: 'Audio System', icon: Monitor },
  { key: 'video', label: 'Video System', icon: Monitor },
  { key: 'network', label: 'Networking System', icon: Wifi },
  { key: 'security', label: 'Surveillance System', icon: ShieldCheck },
  { key: 'controlSystems', label: 'Control System', icon: Radio },
];

// Helper functions for category display
const getCategoryLabel = (key: string) => {
  const category = SYSTEM_CATEGORIES.find(cat => cat.key === key);
  return category ? category.label : key;
};

interface PackageBuilderProps {
  onClose: () => void;
  onPackageCreated?: (packageId: string) => void;
}

interface SelectedProduct extends ProductItem {
  selectedQuantity: number;
  customPrice?: number;
}

export const PackageBuilder: React.FC<PackageBuilderProps> = ({ onClose, onPackageCreated }) => {
  const { createPackage, addItemToPackage, isCreating } = useCustomPackages();
  const { products, isLoading: productsLoading } = usePackageManagement();

  const [packageDetails, setPackageDetails] = useState<CreatePackageRequest>({
    name: '',
    description: '',
    systemCategory: '',
    subcategories: [],
    markupPercentage: 10,
    isDefault: false,
  });

  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Use standard system categories instead of dynamic ones from products
  const categories = SYSTEM_CATEGORIES;

  // Filter products based on search and category (with null check)
  const filteredProducts = (products || []).filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.manufacturer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.systemCategory === selectedCategory;
    const notAlreadySelected = !selectedProducts.find(sp => sp.id === product.id);

    return matchesSearch && matchesCategory && notAlreadySelected;
  });

  // Add product to package
  const addProduct = (product: ProductItem) => {
    const selectedProduct: SelectedProduct = {
      ...product,
      selectedQuantity: 1,
    };
    setSelectedProducts(prev => [...prev, selectedProduct]);
  };

  // Remove product from package
  const removeProduct = (productId: string) => {
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));
  };

  // Update product quantity
  const updateProductQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeProduct(productId);
      return;
    }

    setSelectedProducts(prev =>
      prev.map(p => p.id === productId ? { ...p, selectedQuantity: quantity } : p)
    );
  };

  // Update custom price
  const updateCustomPrice = (productId: string, customPrice: number | undefined) => {
    setSelectedProducts(prev =>
      prev.map(p => p.id === productId ? { ...p, customPrice } : p)
    );
  };

  // Calculate package totals
  const calculateTotals = (): PackagePricingCalculation => {
    const itemBreakdown = selectedProducts.map(product => {
      const unitPrice = product.customPrice || product.unitCost;
      const lineTotal = unitPrice * product.selectedQuantity;

      return {
        productId: product.id,
        productName: product.name,
        quantity: product.selectedQuantity,
        unitPrice: product.unitCost,
        customPrice: product.customPrice,
        lineTotal,
      };
    });

    const subtotal = itemBreakdown.reduce((sum, item) => sum + item.lineTotal, 0);
    const markupAmount = subtotal * (packageDetails.markupPercentage || 0) / 100;
    const finalPrice = subtotal + markupAmount;

    return {
      subtotal,
      markup: packageDetails.markupPercentage || 0,
      markupAmount,
      finalPrice,
      itemBreakdown,
    };
  };

  const totals = calculateTotals();

  // Handle package creation
  const handleCreatePackage = async () => {
    if (!packageDetails.name.trim()) {
      alert('Please enter a package name');
      return;
    }

    if (selectedProducts.length === 0) {
      alert('Please add at least one product to the package');
      return;
    }

    if (!packageDetails.systemCategory) {
      alert('Please select a system category');
      return;
    }

    try {
      const packageId = await createPackage(packageDetails);

      if (packageId) {
        // Add all selected products to the package
        for (const product of selectedProducts) {
          await addItemToPackage({
            packageId,
            productId: product.id,
            quantity: product.selectedQuantity,
            customPrice: product.customPrice,
          });
        }

        if (onPackageCreated) {
          onPackageCreated(packageId);
        }
      }

      onClose();
    } catch (error) {
      console.error('Error creating package:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-background border rounded-lg shadow-xl w-full h-full sm:max-w-7xl sm:max-h-[95vh] overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Compact Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-bold">Create Custom Package</h2>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex-1 overflow-hidden flex flex-col lg:flex-row">
            {/* Left Panel - Package Details & Product Selection */}
            <div className="lg:w-1/2 p-4 lg:border-r flex flex-col">
              {/* Fixed Package Details */}
              <Card className="mb-4 flex-shrink-0">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Package Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="packageName" className="text-sm">Package Name *</Label>
                      <Input
                        id="packageName"
                        value={packageDetails.name}
                        onChange={(e) => setPackageDetails(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter package name"
                        className="h-9"
                      />
                    </div>

                    <div>
                      <Label htmlFor="systemCategory" className="text-sm">System Category *</Label>
                      <Select
                        value={packageDetails.systemCategory}
                        onValueChange={(value) => setPackageDetails(prev => ({ ...prev, systemCategory: value }))}
                      >
                        <SelectTrigger className="h-9">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map(category => (
                            <SelectItem key={category.key} value={category.key}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="packageDescription" className="text-sm">Description</Label>
                    <Textarea
                      id="packageDescription"
                      value={packageDetails.description}
                      onChange={(e) => setPackageDetails(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter package description"
                      rows={2}
                      className="resize-none"
                    />
                  </div>

                  <div className="w-32">
                    <Label htmlFor="markupPercentage" className="text-sm">Markup %</Label>
                    <Input
                      id="markupPercentage"
                      type="number"
                      value={packageDetails.markupPercentage}
                      onChange={(e) => setPackageDetails(prev => ({ ...prev, markupPercentage: parseFloat(e.target.value) || 0 }))}
                      placeholder="10"
                      min="0"
                      step="0.1"
                      className="h-9"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Product Selection with Fixed Header and Scrollable List */}
              <Card className="flex-1 flex flex-col min-h-0">
                <CardHeader className="pb-3 flex-shrink-0">
                  <CardTitle className="text-lg">Add Products</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col min-h-0">
                  {/* Fixed Search and Filter */}
                  <div className="flex gap-2 mb-3 flex-shrink-0">
                    <Input
                      placeholder="Search products..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="flex-1 h-9"
                    />
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="w-40 h-9">
                        <SelectValue placeholder="All categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All categories</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category.key} value={category.key}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Scrollable Product List */}
                  <div className="flex-1 overflow-y-auto space-y-2 min-h-0">
                    {productsLoading ? (
                      <div className="text-center py-4">Loading products...</div>
                    ) : filteredProducts.length === 0 ? (
                      <div className="text-center py-4 text-muted-foreground">
                        {searchTerm || selectedCategory ? 'No products found matching your criteria' : 'No products available'}
                      </div>
                    ) : (
                      filteredProducts.map(product => (
                        <div key={product.id} className="flex items-center gap-3 p-2 border rounded-lg hover:bg-muted/50">
                          {/* Product Image */}
                          <div className="flex-shrink-0 w-12 h-12 bg-muted rounded-lg overflow-hidden">
                            {product.imageUrl ? (
                              <img
                                src={product.imageUrl}
                                alt={product.name}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                  e.currentTarget.nextElementSibling.style.display = 'flex';
                                }}
                              />
                            ) : null}
                            <div
                              className={`w-full h-full flex items-center justify-center text-muted-foreground ${product.imageUrl ? 'hidden' : 'flex'}`}
                            >
                              <Package className="h-6 w-6" />
                            </div>
                          </div>

                          {/* Product Info */}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">{product.name}</div>
                            <div className="text-xs text-gray-500 flex items-center gap-1">
                              <span>{product.manufacturer}</span>
                              {product.model && (
                                <>
                                  <span>•</span>
                                  <span>{product.model}</span>
                                </>
                              )}
                            </div>
                            <div className="text-xs text-gray-500">
                              {product.sku} • ${product.unitCost.toFixed(2)}
                            </div>
                            <Badge variant="secondary" className="text-xs mt-1">
                              {getCategoryLabel(product.systemCategory)}
                            </Badge>
                          </div>

                          {/* Add Button */}
                          <Button
                            size="sm"
                            onClick={() => addProduct(product)}
                            className="flex-shrink-0 h-8 w-8 p-0"
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Panel - Selected Products & Totals */}
            <div className="lg:w-1/2 p-4 overflow-y-auto flex flex-col">
              <Card className="mb-4">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Calculator className="h-4 w-4" />
                    Package Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal:</span>
                      <span>${totals.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Markup ({totals.markup}%):</span>
                      <span>${totals.markupAmount.toFixed(2)}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between font-bold">
                      <span>Total:</span>
                      <span>${totals.finalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="flex-1 flex flex-col">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Selected Products ({selectedProducts.length})</CardTitle>
                </CardHeader>
                <CardContent className="flex-1 overflow-y-auto">
                  {selectedProducts.length === 0 ? (
                    <div className="text-center py-6 text-gray-500">
                      No products selected. Add products from the left panel.
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedProducts.map(product => (
                        <div key={product.id} className="border rounded-lg p-3">
                          <div className="flex items-start gap-3 mb-2">
                            {/* Product Image */}
                            <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-lg overflow-hidden">
                              {product.imageUrl ? (
                                <img
                                  src={product.imageUrl}
                                  alt={product.name}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    e.currentTarget.style.display = 'none';
                                    e.currentTarget.nextElementSibling.style.display = 'flex';
                                  }}
                                />
                              ) : null}
                              <div
                                className={`w-full h-full flex items-center justify-center text-gray-400 ${product.imageUrl ? 'hidden' : 'flex'}`}
                              >
                                <Package className="h-4 w-4" />
                              </div>
                            </div>

                            {/* Product Info */}
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-sm truncate">{product.name}</div>
                              <div className="text-xs text-muted-foreground flex items-center gap-1">
                                <span>{product.manufacturer}</span>
                                {product.model && (
                                  <>
                                    <span>•</span>
                                    <span>{product.model}</span>
                                  </>
                                )}
                              </div>
                              <div className="text-xs text-muted-foreground">{product.sku}</div>
                              <Badge variant="secondary" className="text-xs mt-1">
                                {getCategoryLabel(product.systemCategory)}
                              </Badge>
                            </div>

                            {/* Remove Button */}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeProduct(product.id)}
                              className="text-red-600 hover:text-red-700 h-6 w-6 p-0 flex-shrink-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>

                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label className="text-xs">Quantity</Label>
                              <div className="flex items-center gap-1 mt-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => updateProductQuantity(product.id, product.selectedQuantity - 1)}
                                  disabled={product.selectedQuantity <= 1}
                                  className="h-7 w-7 p-0"
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <Input
                                  type="number"
                                  value={product.selectedQuantity}
                                  onChange={(e) => updateProductQuantity(product.id, parseInt(e.target.value) || 1)}
                                  className="w-12 text-center h-7 text-xs"
                                  min="1"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => updateProductQuantity(product.id, product.selectedQuantity + 1)}
                                  className="h-7 w-7 p-0"
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            <div>
                              <Label className="text-xs">Custom Price</Label>
                              <Input
                                type="number"
                                value={product.customPrice || ''}
                                onChange={(e) => updateCustomPrice(product.id, e.target.value ? parseFloat(e.target.value) : undefined)}
                                placeholder={`$${product.unitCost.toFixed(2)}`}
                                className="mt-1 h-7 text-xs"
                                step="0.01"
                                min="0"
                              />
                            </div>
                          </div>

                          <div className="mt-2 pt-2 border-t flex justify-between text-xs">
                            <span>Line Total:</span>
                            <span className="font-medium">
                              ${((product.customPrice || product.unitCost) * product.selectedQuantity).toFixed(2)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Compact Footer */}
          <div className="p-4 border-t bg-muted/50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {selectedProducts.length} products • Total: ${totals.finalPrice.toFixed(2)}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  onClick={handleCreatePackage}
                  disabled={isCreating || !packageDetails.name.trim() || selectedProducts.length === 0}
                  size="sm"
                  className="min-w-28"
                >
                  {isCreating ? 'Creating...' : 'Create Package'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
