import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Database, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { runPackageMigration, checkMigrationStatus } from '@/utils/runPackageMigration';

export const PackageMigration: React.FC = () => {
  const [migrationStatus, setMigrationStatus] = useState<'checking' | 'needed' | 'completed' | 'running' | 'error'>('checking');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    try {
      setMigrationStatus('checking');
      const isCompleted = await checkMigrationStatus();
      setMigrationStatus(isCompleted ? 'completed' : 'needed');
    } catch (error) {
      console.error('Error checking migration status:', error);
      setMigrationStatus('error');
      setErrorMessage('Failed to check migration status');
    }
  };

  const runMigration = async () => {
    try {
      setMigrationStatus('running');
      setErrorMessage('');
      
      const success = await runPackageMigration();
      
      if (success) {
        setMigrationStatus('completed');
      } else {
        setMigrationStatus('error');
        setErrorMessage('Migration failed. Check console for details.');
      }
    } catch (error) {
      console.error('Migration error:', error);
      setMigrationStatus('error');
      setErrorMessage('Migration failed with an error');
    }
  };

  const getStatusBadge = () => {
    switch (migrationStatus) {
      case 'checking':
        return <Badge variant="secondary">Checking...</Badge>;
      case 'needed':
        return <Badge variant="destructive">Migration Required</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-600">Migration Complete</Badge>;
      case 'running':
        return <Badge variant="secondary">Running...</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getStatusIcon = () => {
    switch (migrationStatus) {
      case 'checking':
      case 'running':
        return <Loader2 className="h-5 w-5 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'needed':
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Database className="h-5 w-5" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Package System Migration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span>Database Status:</span>
          </div>
          {getStatusBadge()}
        </div>

        {migrationStatus === 'needed' && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              The package system requires database migration to create the necessary tables and functions.
              This is a one-time setup process.
            </AlertDescription>
          </Alert>
        )}

        {migrationStatus === 'completed' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Package system is ready! You can now create and manage custom product packages.
            </AlertDescription>
          </Alert>
        )}

        {migrationStatus === 'error' && errorMessage && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {errorMessage}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <h4 className="font-medium">Migration includes:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Product packages table for storing custom packages</li>
            <li>• Package items table for products within packages</li>
            <li>• Automatic cost calculation triggers</li>
            <li>• Row-level security policies for team isolation</li>
            <li>• Database indexes for optimal performance</li>
          </ul>
        </div>

        <div className="flex gap-2">
          {migrationStatus === 'needed' && (
            <Button 
              onClick={runMigration}
              disabled={migrationStatus === 'running'}
              className="flex items-center gap-2"
            >
              {migrationStatus === 'running' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Database className="h-4 w-4" />
              )}
              Run Migration
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={checkStatus}
            disabled={migrationStatus === 'checking' || migrationStatus === 'running'}
          >
            {migrationStatus === 'checking' ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : null}
            Check Status
          </Button>
        </div>

        {migrationStatus === 'completed' && (
          <div className="pt-4 border-t">
            <p className="text-sm text-gray-600">
              The package system is now ready for use. You can create custom packages by combining 
              multiple products from your catalog with custom quantities and pricing.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
