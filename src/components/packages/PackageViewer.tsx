import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  X,
  Package,
  Edit,
  Save,
  Plus,
  Minus,
  Trash2,
  DollarSign,
  ShoppingCart,
  FileText
} from 'lucide-react';
import { ProductPackage, PackageItem } from '@/types/packageTypes';
import { useCustomPackages } from '@/hooks/useCustomPackages';

interface PackageViewerProps {
  package: ProductPackage;
  onClose: () => void;
}

export const PackageViewer: React.FC<PackageViewerProps> = ({ package: pkg, onClose }) => {
  const {
    updatePackageItem,
    removeItemFromPackage,
    calculatePackagePricing
  } = useCustomPackages();

  const [isEditing, setIsEditing] = useState(false);
  const [editingItems, setEditingItems] = useState<Record<string, { quantity: number; customPrice?: number }>>({});

  const pricing = calculatePackagePricing(pkg);

  // Initialize editing state
  const startEditing = () => {
    const initialEditState: Record<string, { quantity: number; customPrice?: number }> = {};
    pkg.items.forEach(item => {
      initialEditState[item.id] = {
        quantity: item.quantity,
        customPrice: item.customPrice,
      };
    });
    setEditingItems(initialEditState);
    setIsEditing(true);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingItems({});
    setIsEditing(false);
  };

  // Save changes
  const saveChanges = async () => {
    try {
      for (const [itemId, changes] of Object.entries(editingItems)) {
        const originalItem = pkg.items.find(item => item.id === itemId);
        if (originalItem &&
            (changes.quantity !== originalItem.quantity ||
             changes.customPrice !== originalItem.customPrice)) {
          await updatePackageItem({
            id: itemId,
            quantity: changes.quantity,
            customPrice: changes.customPrice,
          });
        }
      }
      setIsEditing(false);
      setEditingItems({});
    } catch (error) {
      console.error('Error saving changes:', error);
    }
  };

  // Update editing item
  const updateEditingItem = (itemId: string, field: 'quantity' | 'customPrice', value: number | undefined) => {
    setEditingItems(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        [field]: value,
      },
    }));
  };

  // Remove item
  const handleRemoveItem = async (itemId: string, itemName: string) => {
    if (window.confirm(`Remove "${itemName}" from this package?`)) {
      await removeItemFromPackage(itemId);
    }
  };

  // Calculate edited totals
  const calculateEditedTotals = () => {
    if (!isEditing) return pricing;

    let subtotal = 0;
    pkg.items.forEach(item => {
      const editedItem = editingItems[item.id];
      if (editedItem) {
        const unitPrice = editedItem.customPrice ?? item.product.unitCost;
        subtotal += unitPrice * editedItem.quantity;
      } else {
        const unitPrice = item.customPrice ?? item.product.unitCost;
        subtotal += unitPrice * item.quantity;
      }
    });

    const markupAmount = subtotal * (pkg.markupPercentage / 100);
    const finalPrice = subtotal + markupAmount;

    return {
      subtotal,
      markup: pkg.markupPercentage,
      markupAmount,
      finalPrice,
      itemBreakdown: [], // Not needed for this calculation
    };
  };

  const displayTotals = calculateEditedTotals();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center gap-3">
              <Package className="h-6 w-6 text-blue-600" />
              <div>
                <h2 className="text-2xl font-bold">{pkg.name}</h2>
                {pkg.description && (
                  <p className="text-gray-600">{pkg.description}</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!isEditing ? (
                <Button variant="outline" size="sm" onClick={startEditing}>
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              ) : (
                <>
                  <Button variant="outline" size="sm" onClick={cancelEditing}>
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                  <Button size="sm" onClick={saveChanges}>
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </>
              )}
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-hidden flex">
            {/* Left Panel - Package Items */}
            <div className="flex-1 p-6 overflow-y-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    Package Items ({pkg.items.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {pkg.items.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No items in this package
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {pkg.items.map(item => {
                        const editedItem = editingItems[item.id];
                        const displayQuantity = isEditing ? (editedItem?.quantity ?? item.quantity) : item.quantity;
                        const displayCustomPrice = isEditing ? editedItem?.customPrice : item.customPrice;
                        const unitPrice = displayCustomPrice ?? item.product.unitCost;
                        const lineTotal = unitPrice * displayQuantity;

                        return (
                          <div key={item.id} className="border rounded-lg p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <div className="font-medium">{item.product.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  SKU: {item.product.sku} • {item.product.manufacturer}
                                </div>
                                <Badge variant="secondary" className="text-xs mt-1">
                                  {item.product.systemCategory}
                                </Badge>
                              </div>
                              {isEditing && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveItem(item.id, item.product.name)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                              <div>
                                <Label className="text-xs">Quantity</Label>
                                {isEditing ? (
                                  <div className="flex items-center gap-1 mt-1">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => updateEditingItem(item.id, 'quantity', Math.max(1, displayQuantity - 1))}
                                      disabled={displayQuantity <= 1}
                                    >
                                      <Minus className="h-3 w-3" />
                                    </Button>
                                    <Input
                                      type="number"
                                      value={displayQuantity}
                                      onChange={(e) => updateEditingItem(item.id, 'quantity', parseInt(e.target.value) || 1)}
                                      className="w-16 text-center"
                                      min="1"
                                    />
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => updateEditingItem(item.id, 'quantity', displayQuantity + 1)}
                                    >
                                      <Plus className="h-3 w-3" />
                                    </Button>
                                  </div>
                                ) : (
                                  <div className="mt-1 text-lg font-medium">{displayQuantity}</div>
                                )}
                              </div>

                              <div>
                                <Label className="text-xs">Unit Price</Label>
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    value={displayCustomPrice || ''}
                                    onChange={(e) => updateEditingItem(item.id, 'customPrice', e.target.value ? parseFloat(e.target.value) : undefined)}
                                    placeholder={`$${item.product.unitCost.toFixed(2)}`}
                                    className="mt-1"
                                    step="0.01"
                                    min="0"
                                  />
                                ) : (
                                  <div className="mt-1">
                                    <div className="text-lg font-medium">${unitPrice.toFixed(2)}</div>
                                    {displayCustomPrice && (
                                      <div className="text-xs text-gray-500">
                                        Original: ${item.product.unitCost.toFixed(2)}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>

                              <div>
                                <Label className="text-xs">Line Total</Label>
                                <div className="mt-1 text-lg font-bold text-green-600">
                                  ${lineTotal.toFixed(2)}
                                </div>
                              </div>
                            </div>

                            {item.notes && (
                              <div className="mt-3 pt-3 border-t">
                                <Label className="text-xs">Notes</Label>
                                <div className="text-sm text-gray-600 mt-1">{item.notes}</div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Right Panel - Package Summary */}
            <div className="w-80 p-6 border-l overflow-y-auto">
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Package Summary
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${displayTotals.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Markup ({displayTotals.markup}%):</span>
                      <span>${displayTotals.markupAmount.toFixed(2)}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span className="text-green-600">${displayTotals.finalPrice.toFixed(2)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Package Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <Label className="text-xs">Category</Label>
                      <div className="mt-1">
                        <Badge variant="outline">{pkg.systemCategory}</Badge>
                      </div>
                    </div>

                    {pkg.subcategories && pkg.subcategories.length > 0 && (
                      <div>
                        <Label className="text-xs">Subcategories</Label>
                        <div className="mt-1 flex flex-wrap gap-1">
                          {pkg.subcategories.map(sub => (
                            <Badge key={sub} variant="secondary" className="text-xs">
                              {sub}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <Label className="text-xs">Items Count</Label>
                      <div className="mt-1 text-sm">{pkg.items.length} products</div>
                    </div>

                    <div>
                      <Label className="text-xs">Created</Label>
                      <div className="mt-1 text-sm">
                        {new Date(pkg.createdAt).toLocaleDateString()}
                      </div>
                    </div>

                    <div>
                      <Label className="text-xs">Last Updated</Label>
                      <div className="mt-1 text-sm">
                        {new Date(pkg.updatedAt).toLocaleDateString()}
                      </div>
                    </div>

                    {pkg.isDefault && (
                      <div>
                        <Badge variant="secondary">Default Package</Badge>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
