
import React, { useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useFormContext } from '../context/FormContext';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useNavigate } from 'react-router-dom';

interface FormNavigationProps {
  onNext?: () => boolean | void;
  onPrev?: () => void;
  nextLabel?: string;
  isNextDisabled?: boolean;
  showBackToSummary?: boolean;
  onFinish?: () => void;
}

const FormNavigation: React.FC<FormNavigationProps> = ({
  onNext,
  onPrev,
  nextLabel = "Next",
  isNextDisabled = false,
  showBackToSummary = false,
  onFinish,
}) => {
  const { nextStep, prevStep, formData, goToStep, getTotalSteps } = useFormContext();
  const { currentStep } = formData;
  const totalSteps = getTotalSteps(); // Get dynamic total steps
  const isLastStep = currentStep === totalSteps;
  const isFinalStep = currentStep === totalSteps;
  const summaryStep = 6; // Summary step index with 1-based indexing
  const finalActionsStep = 7; // Final actions step index with 1-based indexing
  const isMobile = useIsMobile();
  const navigate = useNavigate();

  useEffect(() => {
    console.log(`FormNavigation mounted: step ${currentStep || 1}/${totalSteps}, isLastStep: ${isLastStep}, finalStep index: ${finalActionsStep}, summaryStep: ${summaryStep}`);
  }, [currentStep, isLastStep, totalSteps]);

  const handleNext = useCallback(() => {
    console.log("FormNavigation: handleNext called", { 
      isLastStep, 
      onFinish, 
      currentStep: currentStep || 1, 
      totalSteps, 
      finalActionsStep, 
      summaryStep 
    });
    
    // If this is the last step and we have an onFinish handler, call it
    if (isLastStep && onFinish) {
      console.log("FormNavigation: Executing onFinish callback");
      onFinish();
      return;
    }
    
    // For non-final steps or if no onFinish handler exists
    let shouldProceed = true;
    if (onNext) {
      console.log("FormNavigation: Calling onNext handler");
      const result = onNext();
      // Only set shouldProceed to false if result is explicitly false
      if (result === false) {
        console.log("FormNavigation: onNext returned false, not proceeding");
        shouldProceed = false;
      }
    }
    
    if (shouldProceed) {
      // Specifically check if we're on the summary step
      if ((currentStep || 1) === summaryStep) {
        console.log("FormNavigation: Proceeding from Summary to Final Actions explicitly");
        // Ensure we go to the final actions step
        goToStep(finalActionsStep);
      } else {
        console.log("FormNavigation: Proceeding to next step", { currentStep: (currentStep || 1) + 1 });
        nextStep();
      }
    }
  }, [isLastStep, onFinish, onNext, nextStep, currentStep, goToStep, summaryStep, finalActionsStep, totalSteps]);

  const handlePrev = () => {
    if (onPrev) {
      onPrev();
    }
    prevStep();
  };

  const handleGoToSummary = () => {
    goToStep(summaryStep);
  };

  return (
    <div className="flex justify-between mt-6 flex-wrap gap-2">
      <div>
        {(currentStep || 1) > 1 && (
          <Button
            variant="outline"
            onClick={handlePrev}
            className="flex items-center gap-1 md:gap-2 border-[#37372f] hover:bg-[#4a4a3f] hover:text-white text-xs md:text-sm h-10 md:h-11 touch-manipulation"
            size={isMobile ? "sm" : "default"}
          >
            <ArrowLeft className="h-3 w-3 md:h-4 md:w-4" />
            {!isMobile ? "Back" : ""}
          </Button>
        )}
      </div>
      
      <div className="flex gap-2 flex-wrap">
        {showBackToSummary && isFinalStep && (
          <Button
            variant="outline"
            onClick={handleGoToSummary}
            className="flex items-center gap-1 md:gap-2 border-[#37372f] hover:bg-[#4a4a3f] hover:text-white text-xs md:text-sm touch-manipulation"
            size={isMobile ? "sm" : "default"}
          >
            {isMobile ? "Summary" : "Back to Summary"}
          </Button>
        )}
        
        {/* Continue/Next/Finish Button */}
        <Button
          onClick={handleNext}
          disabled={isNextDisabled}
          className="flex items-center gap-1 md:gap-2 bg-[#37372f] hover:bg-[#4a4a3f] text-white text-xs md:text-sm touch-manipulation shadow-sm"
          type="button"
          size={isMobile ? "sm" : "default"}
        >
          {isFinalStep ? (nextLabel || "Finish") : nextLabel}
          {!isFinalStep && <ArrowRight className="h-3 w-3 md:h-4 md:w-4" />}
        </Button>
      </div>
    </div>
  );
};

export default FormNavigation;
