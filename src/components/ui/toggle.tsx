
import * as React from "react"
import * as TogglePrimitive from "@radix-ui/react-toggle"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const toggleVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-all hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-transparent",
        outline:
          "border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",
        soft:
          "bg-muted/40 hover:bg-muted/60 data-[state=on]:bg-primary/20 data-[state=on]:text-primary",
        pill:
          "rounded-full px-3 shadow-sm border data-[state=on]:border-primary/50 data-[state=on]:bg-primary/10",
        modern:
          "relative overflow-hidden bg-background hover:bg-muted/30 data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary/90 data-[state=on]:to-secondary/90 data-[state=on]:text-primary-foreground shadow-sm border border-muted data-[state=on]:border-primary/30",
        glass:
          "bg-background/10 backdrop-blur-sm border border-border/20 hover:bg-background/20 data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary/90 data-[state=on]:to-secondary/90 data-[state=on]:text-primary-foreground shadow-sm transition-all duration-200",
        icon:
          "rounded-full h-9 w-9 bg-transparent hover:bg-muted/20 data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary data-[state=on]:to-secondary data-[state=on]:text-primary-foreground shadow-sm border border-transparent data-[state=on]:border-primary/30",
        gradient:
          "rounded-md data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary data-[state=on]:to-secondary data-[state=on]:text-primary-foreground data-[state=off]:bg-muted/30 shadow-sm transition-all duration-200",
        default_toggle:
          "rounded-md bg-transparent hover:bg-muted/20 data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary/80 data-[state=on]:to-secondary/80 data-[state=on]:text-primary-foreground shadow-sm border border-muted transition-all duration-200",
        package:
          "rounded-md w-full justify-between text-left p-2 bg-transparent hover:bg-muted/10 data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary/90 data-[state=on]:to-secondary/90 data-[state=on]:text-primary-foreground border-transparent",
      },
      size: {
        default: "h-10 px-3",
        sm: "h-8 px-2",
        lg: "h-11 px-5",
        xl: "h-12 px-6",
        icon: "h-9 w-9",
        full: "h-10 px-3 w-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const Toggle = React.forwardRef<
  React.ElementRef<typeof TogglePrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &
    VariantProps<typeof toggleVariants>
>(({ className, variant, size, ...props }, ref) => (
  <TogglePrimitive.Root
    ref={ref}
    className={cn(toggleVariants({ variant, size, className }))}
    {...props}
  />
))

Toggle.displayName = TogglePrimitive.Root.displayName

export { Toggle, toggleVariants }
