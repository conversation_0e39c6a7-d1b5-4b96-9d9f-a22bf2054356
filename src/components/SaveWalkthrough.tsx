
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useFormContext } from '../context/FormContext';
import { useToast } from '@/hooks/use-toast';
import { SaveIcon, UploadIcon } from 'lucide-react';
import { saveWalkthrough, updateWalkthrough } from '@/services/walkthroughService';

interface SaveWalkthroughProps {
  currentId?: string | null;
  onSaved?: (id: string) => void;
}

const SaveWalkthrough: React.FC<SaveWalkthroughProps> = ({ currentId, onSaved }) => {
  const { formData } = useFormContext();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Safely set default title based on client name if available
    // Using optional chaining and nullish coalescing to handle potential undefined values
    if (formData?.clientInfo?.fullName && !title) {
      setTitle(`${formData.clientInfo.fullName}'s Project`);
    }
  }, [formData?.clientInfo?.fullName, title]);

  const handleSave = async (e: React.MouseEvent) => {
    // Prevent any default behavior
    e.preventDefault();
    e.stopPropagation();
    
    if (!title.trim()) {
      toast({
        title: "Error",
        description: "Please enter a title for this walkthrough",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    
    try {
      let id;
      if (currentId) {
        // Ensure formData is available before updating
        if (!formData) {
          throw new Error("Form data is not available");
        }
        
        const success = await updateWalkthrough(currentId, title, formData);
        if (success) {
          id = currentId;
        }
      } else {
        // Ensure formData is available before saving
        if (!formData) {
          throw new Error("Form data is not available");
        }
        
        id = await saveWalkthrough(title, formData);
      }

      if (id) {
        toast({
          title: "Success",
          description: currentId ? "Walkthrough updated successfully" : "Walkthrough saved successfully",
        });
        
        if (onSaved) {
          onSaved(id);
        }
        
        setOpen(false);
      } else {
        throw new Error("Failed to save walkthrough");
      }
    } catch (error) {
      console.error("Error saving walkthrough:", error);
      toast({
        title: "Error",
        description: "Failed to save walkthrough. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="gap-2 text-white bg-[#37372f] hover:bg-[#4a4a3f] border-[#37372f]"
          type="button" // Explicitly set type to button to prevent form submission
        >
          <SaveIcon size={16} />
          {currentId ? "Update Progress" : "Save Progress"}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{currentId ? "Update Walkthrough" : "Save Walkthrough Progress"}</DialogTitle>
          <DialogDescription>
            Save your current progress to continue later.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Title
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter a title"
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button 
            onClick={handleSave} 
            disabled={isSaving}
            className="gap-2 bg-[#37372f] hover:bg-[#4a4a3f] text-white"
            type="button" // Explicitly set type to button
          >
            {isSaving ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                Saving...
              </>
            ) : (
              <>
                <UploadIcon size={16} />
                {currentId ? "Update" : "Save"}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SaveWalkthrough;
