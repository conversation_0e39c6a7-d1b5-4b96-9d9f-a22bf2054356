import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Save, Wand2, Brain, Home } from 'lucide-react';
import AudioRecorder from './AudioRecorder';
import { useFormContext } from '../../context/FormContext';
import { generateRecommendationsStream, StreamCallbacks, getOpenAIKey, saveOpenAIKey } from '@/services/openaiService';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

const AutomatedQualification: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [qualificationText, setQualificationText] = useState('');
  const [scopeOfWork, setScopeOfWork] = useState('');
  const [extractedRooms, setExtractedRooms] = useState<string[]>([]);
  const [extractedSystems, setExtractedSystems] = useState<{[key: string]: boolean}>({});
  const [showExtractedDialog, setShowExtractedDialog] = useState(false);
  const { toast } = useToast();
  const { formData, setFormData, addRoom } = useFormContext();
  const [activeTab, setActiveTab] = useState('record');

  const handleGenerateScopeOfWork = async () => {
    const apiKey = getOpenAIKey();
    
    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please add your OpenAI API key in the Settings page",
        variant: "destructive",
      });
      return;
    }

    if (!qualificationText) {
      toast({
        title: "No Text to Process",
        description: "Please record or enter qualification information first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setScopeOfWork('');
    
    const systemDescription = `
      You are an expert home automation and system integration specialist.
      Based on the client qualification information provided, create a detailed scope of work.
      Format your response in clear sections with project requirements, recommended systems, and technical specifications.
      Be specific about the systems needed based on the information provided.
    `;
    
    const callbacks: StreamCallbacks = {
      onChunk: (chunk) => {
        setScopeOfWork((prev) => prev + chunk);
      },
      onComplete: (fullContent) => {
        setIsProcessing(false);
        toast({
          title: "Scope of Work Generated",
          description: "AI has completed generating the scope of work",
        });
        // After generating scope of work, extract rooms and systems
        extractRoomsAndSystems();
      },
      onError: (error) => {
        setIsProcessing(false);
        toast({
          title: "Error",
          description: error,
          variant: "destructive",
        });
      }
    };
    
    try {
      await generateRecommendationsStream(
        systemDescription,
        qualificationText,
        apiKey,
        callbacks
      );
    } catch (error) {
      setIsProcessing(false);
      toast({
        title: "Error",
        description: "Failed to generate scope of work. Please try again.",
        variant: "destructive",
      });
    }
  };

  const extractRoomsAndSystems = async () => {
    const apiKey = getOpenAIKey();
    
    if (!apiKey || !qualificationText) {
      return;
    }
    
    try {
      setIsProcessing(true);
      
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "system",
              content: "Extract rooms and home automation systems mentioned in the text. Format your response as JSON with two properties: 'rooms' (array of room names) and 'systems' (object with boolean properties for: lighting, shades, audio, video, network, security, controlSystems). Only include systems and rooms explicitly mentioned or clearly implied in the text."
            },
            {
              role: "user",
              content: qualificationText + "\n" + scopeOfWork
            }
          ]
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error?.message || 'Error extracting rooms and systems');
      }
      
      try {
        // Parse the response to extract rooms and systems
        const extractedData = JSON.parse(data.choices[0].message.content);
        
        setExtractedRooms(extractedData.rooms || []);
        setExtractedSystems(extractedData.systems || {});
        
        // Show dialog with extracted data
        setShowExtractedDialog(true);
        
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        toast({
          title: "Error",
          description: "Failed to parse extracted data",
          variant: "destructive",
        });
      }
      
      setIsProcessing(false);
      
    } catch (error) {
      console.error('Error extracting rooms and systems:', error);
      setIsProcessing(false);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to extract rooms and systems',
        variant: "destructive",
      });
    }
  };

  const handleAudioCaptured = async (audioBlob: Blob) => {
    setIsProcessing(true);
    const apiKey = getOpenAIKey();
    
    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please add your OpenAI API key in the Settings page",
        variant: "destructive",
      });
      setIsProcessing(false);
      return;
    }
    
    try {
      // Convert the audio blob to base64
      const reader = new FileReader();
      
      reader.onloadend = async () => {
        const base64Audio = reader.result?.toString().split(',')[1];
        
        if (!base64Audio) {
          throw new Error('Failed to convert audio to base64');
        }
        
        // Create form data for the audio transcription API
        const formData = new FormData();
        formData.append('file', audioBlob, 'audio.webm');
        formData.append('model', 'whisper-1');
        
        // Call OpenAI's audio transcription API
        const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`
          },
          body: formData
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to transcribe audio');
        }
        
        const transcriptionData = await response.json();
        setQualificationText(transcriptionData.text);
        
        toast({
          title: "Audio Transcribed",
          description: "Your audio has been successfully transcribed",
        });
        
        setIsProcessing(false);
        setActiveTab('text');
      };
      
      reader.onerror = () => {
        throw new Error('Failed to read audio file');
      };
      
      reader.readAsDataURL(audioBlob);
    } catch (error) {
      console.error('Error processing audio:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };

  const applyToQualification = () => {
    if (!scopeOfWork.trim()) {
      toast({
        title: "No Scope of Work",
        description: "Please generate a scope of work first",
        variant: "destructive",
      });
      return;
    }
    
    // Update the form data with the generated scope of work
    setFormData(prevData => ({
      ...prevData,
      qualificationInfo: {
        ...prevData.qualificationInfo,
        generatedScopeOfWork: scopeOfWork
      }
    }));
    
    toast({
      title: "Scope of Work Applied",
      description: "The generated scope has been saved to the qualification data",
    });
  };
  
  // Apply extracted rooms to the form
  const applyExtractedData = () => {
    // Apply systems selection
    setFormData(prevData => ({
      ...prevData,
      systemCategories: {
        ...prevData.systemCategories,
        ...extractedSystems
      }
    }));
    
    // Add each extracted room with unique ID and system settings based on extracted systems
    extractedRooms.forEach(roomName => {
      // Pass the extracted systems to be applied to this room
      addRoom(roomName, extractedSystems);
    });
    
    setShowExtractedDialog(false);
    
    toast({
      title: "Data Applied",
      description: `Added ${extractedRooms.length} rooms and updated system selections`,
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Automated Qualification</CardTitle>
        <CardDescription>
          Record client qualification information or enter it manually, then generate a scope of work
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="record">Record Audio</TabsTrigger>
            <TabsTrigger value="text">Edit Text</TabsTrigger>
          </TabsList>
          
          <TabsContent value="record" className="min-h-[200px] flex items-center justify-center">
            <AudioRecorder 
              onAudioCaptured={handleAudioCaptured}
              isProcessing={isProcessing}
            />
          </TabsContent>
          
          <TabsContent value="text">
            <Textarea
              placeholder="Client qualification information will appear here after recording, or enter it manually..."
              className="min-h-[200px]"
              value={qualificationText}
              onChange={(e) => setQualificationText(e.target.value)}
            />
          </TabsContent>
        </Tabs>
        
        <div className="space-y-2">
          <Button
            onClick={handleGenerateScopeOfWork}
            disabled={isProcessing || !qualificationText}
            className="w-full bg-[#37372f] hover:bg-[#4a4a3f] text-white"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="mr-2 h-4 w-4" />
                Generate Scope of Work
              </>
            )}
          </Button>
        </div>
        
        {(scopeOfWork || isProcessing) && (
          <div className="space-y-2">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Generated Scope of Work
            </h3>
            <Textarea
              readOnly
              className="min-h-[300px]"
              placeholder={isProcessing ? "Generating scope of work..." : "Your scope of work will appear here..."}
              value={scopeOfWork}
            />
          </div>
        )}
      </CardContent>
      
      {scopeOfWork && (
        <CardFooter className="flex justify-end gap-2">
          <Button variant="outline" onClick={extractRoomsAndSystems}>
            <Home className="mr-2 h-4 w-4" />
            Extract Rooms & Systems
          </Button>
          <Button onClick={applyToQualification}>
            <Save className="mr-2 h-4 w-4" />
            Save to Qualification
          </Button>
        </CardFooter>
      )}
      
      {/* Dialog to show extracted rooms and systems */}
      <Dialog open={showExtractedDialog} onOpenChange={setShowExtractedDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Extracted Rooms & Systems</DialogTitle>
            <DialogDescription>
              The following information was detected from the qualification. Would you like to add these to your walkthrough?
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Rooms:</h4>
              <div className="grid grid-cols-2 gap-2">
                {extractedRooms.length > 0 ? (
                  extractedRooms.map((room, index) => (
                    <div key={index} className="bg-secondary p-2 rounded-md text-sm">
                      {room}
                    </div>
                  ))
                ) : (
                  <div className="text-muted-foreground col-span-2">No rooms detected</div>
                )}
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium mb-2">Systems:</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(extractedSystems).map(([system, isSelected]) => (
                  isSelected ? (
                    <div key={system} className="bg-primary/10 p-2 rounded-md text-sm flex items-center gap-2">
                      <span className="h-2 w-2 rounded-full bg-primary"></span>
                      {system.charAt(0).toUpperCase() + system.slice(1)}
                    </div>
                  ) : null
                ))}
                {Object.values(extractedSystems).filter(Boolean).length === 0 && (
                  <div className="text-muted-foreground col-span-2">No systems detected</div>
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExtractedDialog(false)}>
              Cancel
            </Button>
            <Button onClick={applyExtractedData}>
              Apply to Walkthrough
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default AutomatedQualification;
