// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nlfmaapgqvomssugaxzw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5sZm1hYXBncXZvbXNzdWdheHp3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0MDU0NTIsImV4cCI6MjA2MTk4MTQ1Mn0.Em3zzRB6-PyDae2rbL1XR6m0UteYzNWKfsBp_CVzWS0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);