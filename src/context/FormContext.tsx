import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { FormData, FormContextType, FormProviderProps, SystemCategories } from '@/types/formTypes';
import { RoomConfig } from '@/types/roomTypes';
import { initialFormData } from './initialFormData';
import { saveWalkthrough, getWalkthrough } from '@/services/walkthroughService';
import { useToast } from '@/hooks/use-toast';
import { applyDefaultPackages } from '@/services/zapierService';
import { shouldSkipSystemStep, createNewRoom } from '@/utils/formUtils';
import { useFormNavigation } from '@/hooks/useFormNavigation';

const FormContext = createContext<FormContextType | null>(null);

export const useFormContext = () => {
  const context = useContext(FormContext);
  if (context === null) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
};

export const FormProvider: React.FC<FormProviderProps> = ({ 
  children, 
  initialClientInfo,
  initialWalkthroughData,
  startAtQualification = false
}) => {
  const { toast } = useToast();
  const [currentWalkthroughId, setCurrentWalkthroughId] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<FormData>(() => {
    if (initialWalkthroughData && 'form_data' in initialWalkthroughData) {
      // If we have a Walkthrough object with form_data, use that
      return initialWalkthroughData.form_data;
    } else if (initialWalkthroughData) {
      // If we have a FormData object directly
      return initialWalkthroughData as FormData;
    }
    
    // Otherwise, use initial data with any client info passed in
    const data = {
      ...initialFormData,
      clientInfo: {
        ...initialFormData.clientInfo,
        ...(initialClientInfo || {})
      },
      // Ensure we start with step 1 (1-based indexing)
      currentStep: startAtQualification ? 2 : 1
    };
    
    // Apply default packages if systemCategories are already set
    if (data.systemCategories && Object.values(data.systemCategories).some(enabled => enabled)) {
      // Create a Record<string, boolean> from systemCategories
      const systemCategoriesRecord: Record<string, boolean> = {};
      Object.entries(data.systemCategories).forEach(([key, value]) => {
        systemCategoriesRecord[key] = !!value;
      });
      
      const defaultPkgs = applyDefaultPackages(systemCategoriesRecord);
      console.log('Applied default packages:', defaultPkgs);
    }
    
    return data;
  });

  const { nextStep, prevStep, goToStep, getTotalSteps } = useFormNavigation({ 
    formData, 
    setFormData, 
    totalBaseSteps: 7,
    startAtQualification
  });

  // The rest of the FormContext component remains the same
  const getClientTier = () => {
    // Use budget to determine client tier
    const budget = formData?.qualificationInfo?.budget || 0;
    if (budget >= 250000) return "Premium";
    if (budget >= 100000) return "Standard";
    return "Basic";
  };
  
  // Load a saved walkthrough by ID
  const loadSavedWalkthrough = async (id: string) => {
    try {
      const walkthroughData = await getWalkthrough(id);
      if (walkthroughData && walkthroughData.form_data) {
        // Fix: Extract form_data from walkthroughData instead of using the whole object
        setFormData(walkthroughData.form_data);
        setCurrentWalkthroughId(id);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error loading walkthrough:", error);
      toast({
        title: "Error",
        description: "Failed to load the saved walkthrough.",
        variant: "destructive",
      });
      return false;
    }
  };
  
  // Reset the form to initial state
  const resetForm = () => {
    setFormData({...initialFormData, currentStep: 1});
    setCurrentWalkthroughId(null);
  };

  const addRoom = (roomName: string, systemSettings?: Partial<Record<string, boolean>>) => {
    setFormData((prev) => {
      // Get system categories to set room options
      const systemCats = prev.systemCategories || {
        lighting: false,
        shades: false,
        audio: false,
        video: false,
        network: false,
        security: false,
        controlSystems: false,
      };
      
      // Convert SystemCategories to Record<string, boolean> before passing to createNewRoom
      const systemCatsRecord: Record<string, boolean> = {};
      Object.entries(systemCats).forEach(([key, value]) => {
        systemCatsRecord[key] = !!value;
      });
      
      // Use the createNewRoom utility function to create a new room
      const newRoom = createNewRoom(roomName, systemCatsRecord);
      
      return {
        ...prev,
        rooms: [...prev.rooms, newRoom]
      };
    });
  };

  const updateRoom = (id: string, data: Partial<RoomConfig>) => {
    setFormData((prev) => {
      const updatedRooms = prev.rooms.map((room) => {
        if (room.id === id) {
          return { ...room, ...data };
        }
        return room;
      });
      
      return {
        ...prev,
        rooms: updatedRooms,
      };
    });
  };

  const removeRoom = (id: string) => {
    setFormData((prev) => ({
      ...prev,
      rooms: prev.rooms.filter((room) => room.id !== id),
    }));
  };

  return (
    <FormContext.Provider value={{ 
      formData, 
      setFormData, 
      nextStep, 
      prevStep, 
      goToStep, 
      addRoom,
      updateRoom,
      removeRoom,
      getTotalSteps,
      shouldSkipSystemStep: (stepIndex) => {
        // Convert SystemCategories to Record<string, boolean> before passing to shouldSkipSystemStep
        if (formData?.systemCategories) {
          const systemCatsRecord: Record<string, boolean> = {};
          Object.entries(formData.systemCategories).forEach(([key, value]) => {
            systemCatsRecord[key] = !!value;
          });
          return shouldSkipSystemStep(stepIndex, systemCatsRecord);
        }
        return shouldSkipSystemStep(stepIndex);
      },
      getClientTier,
      currentWalkthroughId,
      setCurrentWalkthroughId,
      loadSavedWalkthrough,
      resetForm
    }}>
      {children}
    </FormContext.Provider>
  );
};
