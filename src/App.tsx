
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./components/theme/ThemeProvider";
import { AuthProvider } from "./context/AuthContext";
import MainLayout from "./components/layouts/MainLayout";
import Index from "./pages/Index";
import Settings from "./pages/Settings";
import CostManager from "./pages/CostManager";
import NotFound from "./pages/NotFound";
import Auth from "./pages/Auth";
import ClientsList from "./pages/ClientsList";
import NewWalkthrough from "./pages/NewWalkthrough";
import ClientWalkthrough from "./pages/ClientWalkthrough";
import Reports from "./pages/Reports";
import ZapierIntegration from "./pages/ZapierIntegration";
import Actions from "./pages/Actions";
import Profile from "./pages/Profile";
import Products from "./pages/Products";
import SubcategoryManagement from "./pages/SubcategoryManagement";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="system" storageKey="innovative-client-discovery-theme">
      <TooltipProvider>
        <Sonner />
        <AuthProvider>
          <BrowserRouter>
            <Routes>
              <Route path="/auth" element={<Auth />} />
              <Route element={<MainLayout />}>
                <Route path="/" element={<Index />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/cost-manager" element={<CostManager />} />
                <Route path="/new-walkthrough" element={<NewWalkthrough />} />
                <Route path="/edit-walkthrough/:id" element={<ClientWalkthrough />} />
                <Route path="/clients" element={<ClientsList />} />
                <Route path="/reports" element={<Reports />} />
                <Route path="/zapier" element={<ZapierIntegration />} />
                <Route path="/actions" element={<Actions />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/products" element={<Products />} />
                <Route path="/subcategories" element={<SubcategoryManagement />} />
              </Route>
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </AuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
