import { ProductItem } from '@/hooks/usePackageManagement';

// Interface for a product package
export interface ProductPackage {
  id: string;
  name: string;
  description?: string;
  systemCategory: string;
  subcategories: string[];
  totalCost: number;
  markupPercentage: number;
  finalPrice: number;
  isDefault: boolean;
  createdBy?: string;
  teamId?: string;
  createdAt: string;
  updatedAt: string;
  items: PackageItem[];
}

// Interface for items within a package
export interface PackageItem {
  id: string;
  packageId: string;
  productId: string;
  product: ProductItem; // Full product details
  quantity: number;
  customPrice?: number; // Optional price override
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Interface for creating a new package
export interface CreatePackageRequest {
  name: string;
  description?: string;
  systemCategory: string;
  subcategories?: string[];
  markupPercentage?: number;
  isDefault?: boolean;
}

// Interface for adding items to a package
export interface AddPackageItemRequest {
  packageId: string;
  productId: string;
  quantity: number;
  customPrice?: number;
  notes?: string;
}

// Interface for updating package items
export interface UpdatePackageItemRequest {
  id: string;
  quantity?: number;
  customPrice?: number;
  notes?: string;
}

// Interface for package builder state
export interface PackageBuilderState {
  selectedProducts: ProductItem[];
  packageDetails: Partial<CreatePackageRequest>;
  totalCost: number;
  finalPrice: number;
}

// Interface for package search and filtering
export interface PackageFilters {
  systemCategory?: string;
  subcategory?: string;
  searchTerm?: string;
  isDefault?: boolean;
  priceRange?: {
    min: number;
    max: number;
  };
}

// Interface for package statistics
export interface PackageStats {
  totalPackages: number;
  packagesByCategory: Record<string, number>;
  averagePackageValue: number;
  totalValue: number;
}

// Interface for package export data
export interface PackageExportData {
  package: ProductPackage;
  items: Array<{
    productName: string;
    productSku: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    customPrice?: number;
    notes?: string;
  }>;
  summary: {
    totalItems: number;
    subtotal: number;
    markup: number;
    finalTotal: number;
  };
}

// Interface for bulk package operations
export interface BulkPackageOperation {
  packageIds: string[];
  operation: 'delete' | 'duplicate' | 'export' | 'updateCategory';
  data?: any;
}

// Interface for package templates
export interface PackageTemplate {
  id: string;
  name: string;
  description?: string;
  systemCategory: string;
  templateItems: Array<{
    productSku: string;
    quantity: number;
    notes?: string;
  }>;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
}

// Interface for package comparison
export interface PackageComparison {
  packages: ProductPackage[];
  comparison: {
    commonItems: ProductItem[];
    uniqueItems: Record<string, ProductItem[]>;
    priceDifference: number;
    itemCountDifference: number;
  };
}

// Enum for package actions
export enum PackageAction {
  CREATE = 'create',
  EDIT = 'edit',
  DELETE = 'delete',
  DUPLICATE = 'duplicate',
  ADD_ITEM = 'add_item',
  REMOVE_ITEM = 'remove_item',
  UPDATE_ITEM = 'update_item',
  EXPORT = 'export',
  IMPORT = 'import'
}

// Interface for package history/audit
export interface PackageAuditLog {
  id: string;
  packageId: string;
  action: PackageAction;
  userId: string;
  timestamp: string;
  changes: Record<string, any>;
  previousValues?: Record<string, any>;
}

// Interface for package validation
export interface PackageValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Interface for package pricing calculation
export interface PackagePricingCalculation {
  subtotal: number;
  markup: number;
  markupAmount: number;
  finalPrice: number;
  itemBreakdown: Array<{
    productId: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    customPrice?: number;
    lineTotal: number;
  }>;
}

// Interface for package builder context
export interface PackageBuilderContextType {
  currentPackage: ProductPackage | null;
  selectedProducts: ProductItem[];
  packageDetails: Partial<CreatePackageRequest>;
  isBuilding: boolean;
  addProduct: (product: ProductItem, quantity?: number) => void;
  removeProduct: (productId: string) => void;
  updateProductQuantity: (productId: string, quantity: number) => void;
  updatePackageDetails: (details: Partial<CreatePackageRequest>) => void;
  calculateTotals: () => PackagePricingCalculation;
  savePackage: () => Promise<string | null>;
  resetBuilder: () => void;
  loadPackage: (packageId: string) => Promise<void>;
}
