
export interface RoomSystemSettings {
  lighting?: boolean;
  shades?: boolean;
  audio?: boolean;
  video?: boolean;
  network?: boolean;
  security?: boolean;
  controlSystems?: boolean;
  [key: string]: boolean | undefined;
}

export interface RoomConfig {
  id: string;
  name: string;
  systemSettings: RoomSystemSettings;
  floor?: number | string; // Added floor property
  notes?: string;
  images?: string[];
  // Additional properties needed by multiple components
  lighting?: boolean;
  lightingNotes?: string;
  shades?: "none" | "manual" | "motorized";
  audio?: "none" | "hidden" | "in-ceiling";
  video?: "none" | "display" | "projector";
  network?: "standard" | "high density";
  surveillance?: boolean;
  control?: "none" | "wall keypad" | "remote" | "app";
  photos?: File[];
  [key: string]: any;
}
