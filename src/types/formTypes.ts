
import { ReactNode } from 'react';
import { RoomConfig } from './roomTypes';

export interface ClientInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string;
  // Compatibility fields for existing components
  fullName?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  architectInfo?: string;
  homeType?: string;
  projectType?: string;
}

export interface QualificationInfo {
  budget: number;
  timeline: string;
  purpose: string;
  generatedScopeOfWork: string;
  // Compatibility fields for existing components
  squareFootage?: number;
  floors?: number;
  projectStage?: string;
  completionDate?: Date | string | null;
}

export interface SystemCategories {
  lighting: boolean;
  shades: boolean;
  audio: boolean;
  video: boolean;
  network: boolean;
  security: boolean;
  controlSystems: boolean;
  [key: string]: boolean; // Add index signature to make it compatible with Record<string, boolean>
}

// New interface for system subcategories
export interface SystemSubcategory {
  id: string;
  name: string;
  systemType: string;
  enabled: boolean;
}

export interface SerializedFile {
  name: string;
  size: number;
  type: string;
  lastModified: number;
  data: string; // base64 encoded file data
  category: 'floorplans' | 'references' | 'general';
}

export interface ProjectFiles {
  floorplans: File[];
  references: File[];
  general?: File[]; // Add general files array for other document types
}

export interface SerializedProjectFiles {
  floorplans: SerializedFile[];
  references: SerializedFile[];
  general: SerializedFile[];
}

export interface FormData {
  clientInfo: ClientInfo;
  qualificationInfo: QualificationInfo;
  systemCategories: SystemCategories;
  rooms: RoomConfig[];
  projectFiles?: ProjectFiles;
  currentStep: number;
  [key: string]: unknown;
}

export interface FormContextType {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  addRoom: (roomName: string, systemSettings?: Partial<Record<string, boolean>>) => void;
  updateRoom: (id: string, data: any) => void;
  removeRoom: (id: string) => void;
  getTotalSteps: () => number;
  shouldSkipSystemStep: (stepIndex: number) => boolean;
  getClientTier: () => string;
  currentWalkthroughId: string | null;
  setCurrentWalkthroughId: (id: string | null) => void;
  loadSavedWalkthrough: (id: string) => Promise<boolean>;
  resetForm: () => void;
}

export interface FormProviderProps {
  children: ReactNode;
  initialClientInfo?: any;
  initialWalkthroughData?: FormData | Walkthrough;
  startAtQualification?: boolean;
}

export interface Walkthrough {
  id?: string;
  created_at?: string;
  title: string;
  client_name: string;
  form_data: FormData;
  last_step: number;
}

// New interface for team member profile data
export interface TeamMemberProfile {
  id?: string;
  user_id?: string;
  full_name?: string | null;
  avatar_url?: string | null;
  [key: string]: any; // Allow additional properties
}

// New interface for team member
export interface TeamMember {
  id: string;
  team_id: string;
  user_id: string;
  profile?: TeamMemberProfile | null;
  role?: string;
  created_at?: string;
}

// New product catalog interfaces
export interface ProductSubcategory {
  id: string;
  name: string;
  systemType: string;  // Maps to SystemCategories key
}
