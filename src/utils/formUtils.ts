
import { RoomConfig } from '../types/roomTypes';

export const shouldSkipSystemStep = (stepIndex: number, systemCategories?: Record<string, boolean>): boolean => {
  // If the step is room walkthrough (4) but no systems are selected, skip it
  if (stepIndex === 4) {
    if (!systemCategories) {
      return true;
    }
    return !Object.values(systemCategories).some(value => value);
  }
  return false;
};

// Add the createNewRoom function that was missing
export const createNewRoom = (roomName: string = "Untitled Room", systemSettings?: Record<string, boolean>): RoomConfig => {
  // Create a unique ID for the room
  const roomId = `room-${Date.now()}`;
  
  // Default system settings if none provided
  const systems = systemSettings || {
    lighting: false,
    shades: false,
    audio: false,
    video: false,
    network: false,
    security: false,
    controlSystems: false
  };
  
  // Create and return the new room config
  return {
    id: roomId,
    name: roomName,
    floor: "1", // Default to 1st floor
    systemSettings: {
      lighting: systems.lighting || false,
      shades: systems.shades || false,
      audio: systems.audio || false,
      video: systems.video || false,
      network: systems.network || false,
      security: systems.security || false,
      controlSystems: systems.controlSystems || false
    },
    lighting: systems.lighting || false,
    lightingNotes: "",
    shades: systems.shades ? "manual" : "none",
    audio: systems.audio ? "hidden" : "none",
    video: systems.video ? "display" : "none",
    network: systems.network ? "standard" : "standard",
    surveillance: systems.security || false,
    control: systems.controlSystems ? "wall keypad" : "none",
    notes: "",
    photos: []
  };
};
