import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format } from 'date-fns';
import { Quote } from '@/models/Quote';

// Add a debounce flag to prevent multiple generations
let isGenerating = false;

export const generateQuotePDF = (quote: Quote) => {
  // If already generating, exit early
  if (isGenerating) {
    console.log('Quote PDF generation already in progress - preventing duplicate generation');
    return;
  }

  isGenerating = true;

  try {
    const { costStructure, costSummary } = quote;

    // Create a new document
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Set up variables for positioning
    let currentY = 25;
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);

    // Define modern color palette (matching system colors)
    const colors = {
      primary: [55, 55, 47],      // #37372f - Primary dark olive
      secondary: [74, 74, 63],    // #4a4a3f - Secondary lighter olive  
      accent: [59, 130, 246],     // #3b82f6 - Blue accent
      muted: [156, 163, 175],     // #9ca3af - Muted gray
      background: [249, 250, 251], // #f9fafb - Light background
      text: [17, 24, 39],         // #111827 - Dark text
      border: [229, 231, 235]     // #e5e7eb - Border gray
    };

    // Helper function to add modern card-style section
    const addCardSection = (title: string, startY: number, height: number = 0) => {
      // Add card background
      doc.setFillColor(...colors.background);
      doc.roundedRect(margin, startY, contentWidth, height || 20, 2, 2, 'F');
      
      // Add card border
      doc.setDrawColor(...colors.border);
      doc.setLineWidth(0.5);
      doc.roundedRect(margin, startY, contentWidth, height || 20, 2, 2, 'S');
      
      // Add section title
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.setTextColor(...colors.primary);
      doc.text(title, margin + 5, startY + 8);
      
      return startY + 15;
    };

    // Add modern header with background
    doc.setFillColor(...colors.primary);
    doc.rect(0, 0, pageWidth, 35, 'F');
    
    // Add header text
    doc.setFont("helvetica", "bold");
    doc.setFontSize(24);
    doc.setTextColor(255, 255, 255); // White text
    doc.text("PROJECT QUOTE", pageWidth / 2, 20, { align: "center" });
    
    // Add subtitle
    doc.setFont("helvetica", "normal");
    doc.setFontSize(12);
    doc.setTextColor(220, 220, 220); // Light gray text
    doc.text("Professional Cost Estimate", pageWidth / 2, 28, { align: "center" });
    
    currentY = 45;

    // Add logo (if available)
    const logo = new Image();
    logo.src = '/lovable-uploads/b236b3b0-3175-4672-8fad-ee4e29a85315.png';
    
    // Use a Promise to handle logo loading
    const loadLogoPromise = new Promise<void>((resolve) => {
      logo.onload = () => {
        // Add logo to the header (white version for dark background)
        const logoWidth = 25;
        const logoHeight = logoWidth * (logo.height / logo.width);
        doc.addImage(logo, 'PNG', pageWidth - margin - logoWidth, 8, logoWidth, logoHeight);
        resolve();
      };
      
      // Handle loading failures with a timeout
      const timeout = setTimeout(() => {
        console.warn("Logo image loading timed out, generating PDF without logo");
        resolve();
      }, 2000);
      
      logo.onerror = () => {
        console.warn("Error loading logo, generating PDF without logo");
        clearTimeout(timeout);
        resolve();
      };
    });

    // Wait for the logo to load (or timeout) then continue with PDF generation
    loadLogoPromise.then(() => {
      // Add date and quote info
      doc.setFont("helvetica", "normal");
      doc.setFontSize(10);
      doc.setTextColor(...colors.muted);
      doc.text(`Quote Date: ${format(quote.createdAt, 'MMMM d, yyyy')}`, pageWidth - margin, currentY, { align: "right" });
      doc.text(`Quote ID: ${quote.id.split('-')[1]}`, pageWidth - margin, currentY + 5, { align: "right" });
      if (quote.validUntil) {
        doc.text(`Valid Until: ${format(quote.validUntil, 'MMMM d, yyyy')}`, pageWidth - margin, currentY + 10, { align: "right" });
      }
      currentY += 20;

      // Quote Information Section
      currentY = addCardSection("QUOTE INFORMATION", currentY, 35);
      
      doc.setFont("helvetica", "normal");
      doc.setFontSize(11);
      doc.setTextColor(...colors.text);
      
      const leftCol = margin + 8;
      const rightCol = margin + (contentWidth / 2) + 5;
      let leftY = currentY;
      let rightY = currentY;
      
      // Left column
      const leftFields = [
        ['Quote Title', quote.title],
        ['Client Name', quote.clientName],
        ['Client Email', quote.clientEmail || 'Not specified']
      ];
      
      leftFields.forEach(([label, value]) => {
        doc.setFont("helvetica", "bold");
        doc.setFontSize(10);
        doc.setTextColor(...colors.primary);
        doc.text(label + ':', leftCol, leftY);
        
        doc.setFont("helvetica", "normal");
        doc.setTextColor(...colors.text);
        doc.text(value, leftCol, leftY + 4);
        leftY += 10;
      });
      
      // Right column
      const rightFields = [
        ['Project Type', quote.projectType || 'Not specified'],
        ['Status', quote.status.charAt(0).toUpperCase() + quote.status.slice(1)]
      ];
      
      rightFields.forEach(([label, value]) => {
        doc.setFont("helvetica", "bold");
        doc.setFontSize(10);
        doc.setTextColor(...colors.primary);
        doc.text(label + ':', rightCol, rightY);
        
        doc.setFont("helvetica", "normal");
        doc.setTextColor(...colors.text);
        doc.text(value, rightCol, rightY + 4);
        rightY += 10;
      });

      currentY = Math.max(leftY, rightY) + 15;

      // Cost Breakdown Section
      currentY = addCardSection("COST BREAKDOWN", currentY, 0);
      currentY += 5;

      // Create table data for categories
      const tableData = costStructure.categories.map(category => {
        const categoryTotal = category.items.reduce((total, item) => {
          const itemTotal = item.unitCost * item.quantity + 
            (item.laborHours && item.laborRate ? item.laborHours * item.laborRate : 0);
          const withMarkup = item.markup ? itemTotal * (1 + (item.markup / 100)) : itemTotal;
          return total + withMarkup;
        }, 0);
        
        return [
          category.name,
          category.description || '',
          `$${categoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
        ];
      });

      autoTable(doc, {
        startY: currentY,
        head: [['Category', 'Description', 'Amount']],
        body: tableData,
        margin: { left: margin, right: margin },
        headStyles: { 
          fillColor: colors.primary,
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 11
        },
        styles: { 
          fontSize: 10,
          textColor: colors.text,
          lineColor: colors.border,
          lineWidth: 0.5
        },
        alternateRowStyles: {
          fillColor: colors.background
        },
        theme: 'grid',
        tableWidth: contentWidth
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;

      // Cost Summary Section
      currentY = addCardSection("COST SUMMARY", currentY, 50);
      
      const summaryLeftCol = margin + 8;
      const summaryRightCol = margin + contentWidth - 80;
      let summaryY = currentY;

      const formatCurrency = (amount: number) => {
        return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      };

      // Summary items
      const summaryItems = [
        ['Subtotal', formatCurrency(costSummary.subtotal)],
        ['Tax', formatCurrency(costSummary.tax)],
        ...(costSummary.discount > 0 ? [['Discount', `-${formatCurrency(costSummary.discount)}`]] : [])
      ];

      summaryItems.forEach(([label, value]) => {
        doc.setFont("helvetica", "normal");
        doc.setFontSize(11);
        doc.setTextColor(...colors.text);
        doc.text(label, summaryLeftCol, summaryY);
        doc.text(value, summaryRightCol, summaryY, { align: 'right' });
        summaryY += 8;
      });

      // Total line
      doc.setLineWidth(0.5);
      doc.setDrawColor(...colors.border);
      doc.line(summaryLeftCol, summaryY, summaryRightCol, summaryY);
      summaryY += 8;

      doc.setFont("helvetica", "bold");
      doc.setFontSize(14);
      doc.setTextColor(...colors.primary);
      doc.text('TOTAL', summaryLeftCol, summaryY);
      doc.text(formatCurrency(costSummary.total), summaryRightCol, summaryY, { align: 'right' });

      currentY = summaryY + 20;

      // Notes section if available
      if (quote.notes) {
        currentY = addCardSection("NOTES", currentY, 0);
        currentY += 5;
        
        doc.setFont("helvetica", "normal");
        doc.setFontSize(10);
        doc.setTextColor(...colors.text);
        
        const noteLines = doc.splitTextToSize(quote.notes, contentWidth - 16);
        doc.text(noteLines, leftCol, currentY);
        currentY += noteLines.length * 5 + 15;
      }

      // Footer
      const totalPages = doc.getNumberOfPages();
      for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);
        
        // Add footer background
        const footerY = doc.internal.pageSize.height - 20;
        doc.setFillColor(...colors.primary);
        doc.rect(0, footerY, pageWidth, 20, 'F');
        
        // Add footer text
        doc.setFont("helvetica", "normal");
        doc.setFontSize(9);
        doc.setTextColor(255, 255, 255);
        doc.text(
          `Page ${i} of ${totalPages}`, 
          margin, 
          footerY + 12
        );
        
        doc.text(
          `Innovative Technology Solutions | ${format(new Date(), 'yyyy')}`, 
          pageWidth - margin, 
          footerY + 12, 
          { align: "right" }
        );
      }

      // Save the PDF
      const fileName = `${quote.clientName.replace(/[^a-z0-9]/gi, '_')}_Quote_${quote.id.split('-')[1]}.pdf`;
      doc.save(fileName);

      // Reset the generating flag
      isGenerating = false;
    });

  } catch (error) {
    console.error('Error generating quote PDF:', error);
    // Reset the generating flag in case of error
    isGenerating = false;
    throw error;
  }
};
