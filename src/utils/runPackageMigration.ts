import { supabase } from '@/integrations/supabase/client';

// SQL for creating the package tables
const packageMigrationSQL = `
-- Create product_packages table for custom packages
CREATE TABLE IF NOT EXISTS public.product_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  system_category TEXT NOT NULL,
  subcategories TEXT[] DEFAULT '{}',
  total_cost DECIMAL DEFAULT 0,
  markup_percentage DECIMAL DEFAULT 0,
  final_price DECIMAL DEFAULT 0,
  is_default BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES auth.users(id),
  team_id UUID REFERENCES teams(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create product_package_items table for items within packages
CREATE TABLE IF NOT EXISTS public.product_package_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  package_id UUID NOT NULL REFERENCES product_packages(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES product_catalog_items(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  custom_price DECIMAL NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  UNIQUE(package_id, product_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_product_packages_system_category ON public.product_packages(system_category);
CREATE INDEX IF NOT EXISTS idx_product_packages_team_id ON public.product_packages(team_id);
CREATE INDEX IF NOT EXISTS idx_product_packages_created_by ON public.product_packages(created_by);
CREATE INDEX IF NOT EXISTS idx_product_package_items_package_id ON public.product_package_items(package_id);
CREATE INDEX IF NOT EXISTS idx_product_package_items_product_id ON public.product_package_items(product_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.product_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_package_items ENABLE ROW LEVEL SECURITY;
`;

const rlsPoliciesSQL = `
-- Create RLS policies for product_packages
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view packages from their team" ON public.product_packages;
  DROP POLICY IF EXISTS "Users can insert packages for their team" ON public.product_packages;
  DROP POLICY IF EXISTS "Users can update packages from their team" ON public.product_packages;
  DROP POLICY IF EXISTS "Users can delete packages from their team" ON public.product_packages;

  -- Create new policies
  CREATE POLICY "Users can view packages from their team" ON public.product_packages
    FOR SELECT USING (
      team_id = (auth.jwt() ->> 'team_id')::UUID OR
      team_id IS NULL
    );

  CREATE POLICY "Users can insert packages for their team" ON public.product_packages
    FOR INSERT WITH CHECK (
      team_id = (auth.jwt() ->> 'team_id')::UUID OR
      team_id IS NULL
    );

  CREATE POLICY "Users can update packages from their team" ON public.product_packages
    FOR UPDATE USING (
      team_id = (auth.jwt() ->> 'team_id')::UUID OR
      team_id IS NULL
    );

  CREATE POLICY "Users can delete packages from their team" ON public.product_packages
    FOR DELETE USING (
      team_id = (auth.jwt() ->> 'team_id')::UUID OR
      team_id IS NULL
    );
END $$;
`;

const packageItemsPoliciesSQL = `
-- Create RLS policies for product_package_items
DO $$
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view package items from their team packages" ON public.product_package_items;
  DROP POLICY IF EXISTS "Users can insert package items for their team packages" ON public.product_package_items;
  DROP POLICY IF EXISTS "Users can update package items from their team packages" ON public.product_package_items;
  DROP POLICY IF EXISTS "Users can delete package items from their team packages" ON public.product_package_items;

  -- Create new policies
  CREATE POLICY "Users can view package items from their team packages" ON public.product_package_items
    FOR SELECT USING (
      EXISTS (
        SELECT 1 FROM product_packages 
        WHERE id = package_id 
        AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
      )
    );

  CREATE POLICY "Users can insert package items for their team packages" ON public.product_package_items
    FOR INSERT WITH CHECK (
      EXISTS (
        SELECT 1 FROM product_packages 
        WHERE id = package_id 
        AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
      )
    );

  CREATE POLICY "Users can update package items from their team packages" ON public.product_package_items
    FOR UPDATE USING (
      EXISTS (
        SELECT 1 FROM product_packages 
        WHERE id = package_id 
        AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
      )
    );

  CREATE POLICY "Users can delete package items from their team packages" ON public.product_package_items
    FOR DELETE USING (
      EXISTS (
        SELECT 1 FROM product_packages 
        WHERE id = package_id 
        AND (team_id = (auth.jwt() ->> 'team_id')::UUID OR team_id IS NULL)
      )
    );
END $$;
`;

const triggerFunctionSQL = `
-- Create a function to automatically update package total cost when items change
CREATE OR REPLACE FUNCTION update_package_total_cost()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the total cost of the package
  UPDATE product_packages 
  SET 
    total_cost = (
      SELECT COALESCE(SUM(
        CASE 
          WHEN ppi.custom_price IS NOT NULL THEN ppi.custom_price * ppi.quantity
          ELSE pci.unit_cost * ppi.quantity
        END
      ), 0)
      FROM product_package_items ppi
      JOIN product_catalog_items pci ON ppi.product_id = pci.id
      WHERE ppi.package_id = COALESCE(NEW.package_id, OLD.package_id)
    ),
    final_price = (
      SELECT COALESCE(SUM(
        CASE 
          WHEN ppi.custom_price IS NOT NULL THEN ppi.custom_price * ppi.quantity
          ELSE pci.unit_cost * ppi.quantity
        END
      ), 0) * (1 + COALESCE(markup_percentage, 0) / 100)
      FROM product_package_items ppi
      JOIN product_catalog_items pci ON ppi.product_id = pci.id
      WHERE ppi.package_id = COALESCE(NEW.package_id, OLD.package_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.package_id, OLD.package_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update package costs
DROP TRIGGER IF EXISTS trigger_update_package_cost_on_insert ON product_package_items;
DROP TRIGGER IF EXISTS trigger_update_package_cost_on_update ON product_package_items;
DROP TRIGGER IF EXISTS trigger_update_package_cost_on_delete ON product_package_items;

CREATE TRIGGER trigger_update_package_cost_on_insert
  AFTER INSERT ON product_package_items
  FOR EACH ROW EXECUTE FUNCTION update_package_total_cost();

CREATE TRIGGER trigger_update_package_cost_on_update
  AFTER UPDATE ON product_package_items
  FOR EACH ROW EXECUTE FUNCTION update_package_total_cost();

CREATE TRIGGER trigger_update_package_cost_on_delete
  AFTER DELETE ON product_package_items
  FOR EACH ROW EXECUTE FUNCTION update_package_total_cost();
`;

export const runPackageMigration = async (): Promise<boolean> => {
  try {
    console.log('Starting package migration...');

    // Step 1: Create tables and indexes
    console.log('Creating tables and indexes...');
    const { error: tablesError } = await supabase.rpc('exec_sql', { sql: packageMigrationSQL });
    if (tablesError) {
      console.error('Error creating tables:', tablesError);
      return false;
    }

    // Step 2: Create RLS policies for packages
    console.log('Creating RLS policies for packages...');
    const { error: packagePoliciesError } = await supabase.rpc('exec_sql', { sql: rlsPoliciesSQL });
    if (packagePoliciesError) {
      console.error('Error creating package policies:', packagePoliciesError);
      return false;
    }

    // Step 3: Create RLS policies for package items
    console.log('Creating RLS policies for package items...');
    const { error: itemPoliciesError } = await supabase.rpc('exec_sql', { sql: packageItemsPoliciesSQL });
    if (itemPoliciesError) {
      console.error('Error creating package item policies:', itemPoliciesError);
      return false;
    }

    // Step 4: Create trigger function and triggers
    console.log('Creating trigger function and triggers...');
    const { error: triggerError } = await supabase.rpc('exec_sql', { sql: triggerFunctionSQL });
    if (triggerError) {
      console.error('Error creating triggers:', triggerError);
      return false;
    }

    console.log('Package migration completed successfully!');
    return true;
  } catch (error) {
    console.error('Migration failed:', error);
    return false;
  }
};

// Function to check if migration is needed
export const checkMigrationStatus = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('product_packages')
      .select('id')
      .limit(1);
    
    // If no error, tables exist
    return !error;
  } catch (error) {
    // Tables don't exist
    return false;
  }
};

// Export for manual execution
if (typeof window !== 'undefined') {
  (window as any).runPackageMigration = runPackageMigration;
  (window as any).checkMigrationStatus = checkMigrationStatus;
}
