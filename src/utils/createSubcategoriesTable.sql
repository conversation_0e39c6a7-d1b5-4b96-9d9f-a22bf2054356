-- Create product_subcategories table for custom subcategory management
-- Run this SQL in your Supabase SQL editor to create the table

CREATE TABLE IF NOT EXISTS public.product_subcategories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  system_category TEXT NOT NULL,
  order_index INTEGER DEFAULT 0,
  description TEXT,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add comment to the table
COMMENT ON TABLE public.product_subcategories IS 'Stores custom subcategories for product organization';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_product_subcategories_system_category ON public.product_subcategories(system_category);
CREATE INDEX IF NOT EXISTS idx_product_subcategories_order ON public.product_subcategories(system_category, order_index);

-- Create unique constraint to prevent duplicate subcategory names within the same system category
CREATE UNIQUE INDEX IF NOT EXISTS idx_product_subcategories_unique_name 
ON public.product_subcategories(system_category, LOWER(name));

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE public.product_subcategories ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for authenticated users
CREATE POLICY IF NOT EXISTS "Allow all operations for authenticated users" ON public.product_subcategories
FOR ALL USING (true);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER IF NOT EXISTS update_product_subcategories_updated_at 
BEFORE UPDATE ON public.product_subcategories 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default subcategories
INSERT INTO public.product_subcategories (name, system_category, order_index, description, is_default) VALUES
-- Lighting Subcategories
('Recessed', 'lighting', 1, 'Recessed ceiling lights and downlights', true),
('Pendant', 'lighting', 2, 'Hanging pendant lights and chandeliers', true),
('Track', 'lighting', 3, 'Track lighting systems and fixtures', true),
('Smart', 'lighting', 4, 'Smart and connected lighting solutions', true),
('Dimmable', 'lighting', 5, 'Dimmable lighting fixtures and controls', true),
('LED', 'lighting', 6, 'LED lighting fixtures and bulbs', true),
('Outdoor', 'lighting', 7, 'Exterior and landscape lighting', true),
('Under Cabinet', 'lighting', 8, 'Under cabinet and task lighting', true),
('Decorative', 'lighting', 9, 'Decorative and accent lighting', true),

-- Audio Subcategories
('Speakers', 'audio', 1, 'All types of speakers and drivers', true),
('Amplifiers', 'audio', 2, 'Audio amplifiers and power systems', true),
('Receivers', 'audio', 3, 'Audio/video receivers and processors', true),
('Wireless', 'audio', 4, 'Wireless audio systems and components', true),
('Ceiling', 'audio', 5, 'In-ceiling and overhead speakers', true),
('In-Wall', 'audio', 6, 'In-wall speakers and components', true),
('Outdoor', 'audio', 7, 'Weather-resistant outdoor audio', true),
('Subwoofers', 'audio', 8, 'Subwoofers and bass management', true),
('Soundbars', 'audio', 9, 'Soundbars and all-in-one systems', true),

-- Video Subcategories
('Displays', 'video', 1, 'TVs, monitors, and display panels', true),
('Projectors', 'video', 2, 'Video projectors and projection systems', true),
('4K/8K', 'video', 3, 'Ultra-high definition video equipment', true),
('Streaming', 'video', 4, 'Streaming devices and media players', true),
('Cables', 'video', 5, 'Video cables and connectivity', true),
('Mounts', 'video', 6, 'TV and display mounting solutions', true),
('Switchers', 'video', 7, 'Video switchers and matrix systems', true),
('Extenders', 'video', 8, 'Video signal extenders and distribution', true),
('Commercial', 'video', 9, 'Commercial-grade video equipment', true),

-- Security Subcategories
('Cameras', 'security', 1, 'Security cameras and surveillance', true),
('Access Control', 'security', 2, 'Door locks and access systems', true),
('Alarms', 'security', 3, 'Alarm systems and sensors', true),
('Intercoms', 'security', 4, 'Intercom and communication systems', true),
('Motion Sensors', 'security', 5, 'Motion detection and sensors', true),
('Smart Locks', 'security', 6, 'Smart and electronic door locks', true),
('Outdoor', 'security', 7, 'Exterior security equipment', true),
('Indoor', 'security', 8, 'Interior security systems', true),
('Monitoring', 'security', 9, 'Security monitoring and recording', true),

-- Network Subcategories
('Routers', 'network', 1, 'Network routers and gateways', true),
('Switches', 'network', 2, 'Network switches and hubs', true),
('Access Points', 'network', 3, 'Wireless access points and WiFi', true),
('Cables', 'network', 4, 'Network cables and connectivity', true),
('Managed', 'network', 5, 'Managed network equipment', true),
('Unmanaged', 'network', 6, 'Unmanaged network equipment', true),
('PoE', 'network', 7, 'Power over Ethernet equipment', true),
('Fiber', 'network', 8, 'Fiber optic network components', true),
('Enterprise', 'network', 9, 'Enterprise-grade network equipment', true),

-- Shades Subcategories
('Motorized', 'shades', 1, 'Motorized and automated shades', true),
('Manual', 'shades', 2, 'Manual operation window treatments', true),
('Roller', 'shades', 3, 'Roller shades and blinds', true),
('Roman', 'shades', 4, 'Roman shades and fabric treatments', true),
('Cellular', 'shades', 5, 'Cellular and honeycomb shades', true),
('Blackout', 'shades', 6, 'Light-blocking and blackout shades', true),
('Light Filtering', 'shades', 7, 'Light filtering window treatments', true),
('Outdoor', 'shades', 8, 'Exterior shades and awnings', true),
('Smart', 'shades', 9, 'Smart and connected window treatments', true),

-- Control Systems Subcategories
('Processors', 'controlSystems', 1, 'Control processors and hubs', true),
('Touch Panels', 'controlSystems', 2, 'Touch screen control interfaces', true),
('Keypads', 'controlSystems', 3, 'Control keypads and buttons', true),
('Remotes', 'controlSystems', 4, 'Remote controls and handheld devices', true),
('Sensors', 'controlSystems', 5, 'Environmental and occupancy sensors', true),
('Interfaces', 'controlSystems', 6, 'Control interfaces and modules', true),
('Smart Home', 'controlSystems', 7, 'Smart home automation systems', true),
('Commercial', 'controlSystems', 8, 'Commercial control systems', true),
('Integration', 'controlSystems', 9, 'System integration components', true)

ON CONFLICT (system_category, LOWER(name)) DO NOTHING;
