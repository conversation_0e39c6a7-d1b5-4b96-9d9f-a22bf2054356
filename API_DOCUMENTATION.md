# ITS Discovery - API Documentation

## Overview

ITS Discovery uses a combination of Supabase backend services, local storage for temporary data, and external API integrations. This document outlines all API endpoints, data structures, and integration patterns.

## Backend Architecture

### Supabase Integration
- **Database**: PostgreSQL with real-time subscriptions
- **Authentication**: Supabase Auth with JWT tokens
- **Storage**: File uploads and document management
- **Edge Functions**: Server-side logic (planned)

### Data Storage Strategy
- **Persistent Data**: Supabase PostgreSQL database
- **Temporary Data**: Browser localStorage for form state
- **File Storage**: Supabase Storage buckets
- **Cache**: TanStack Query for client-side caching

## Authentication API

### Supabase Auth Endpoints

#### Sign Up
```typescript
const { data, error } = await supabase.auth.signUp({
  email: string,
  password: string,
  options: {
    emailRedirectTo: string
  }
});
```

#### Sign In
```typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: string,
  password: string
});
```

#### Google OAuth
```typescript
const { data, error } = await supabase.auth.signInWithOAuth({
  provider: 'google',
  options: {
    redirectTo: string
  }
});
```

#### Sign Out
```typescript
const { error } = await supabase.auth.signOut();
```

#### Session Management
```typescript
// Get current session
const { data: { session } } = await supabase.auth.getSession();

// Listen to auth changes
supabase.auth.onAuthStateChange((event, session) => {
  // Handle auth state changes
});
```

## Database API

### Walkthroughs Table

#### Schema
```sql
CREATE TABLE walkthroughs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  client_name TEXT NOT NULL,
  form_data JSONB NOT NULL,
  last_step INTEGER DEFAULT 1,
  team_id UUID REFERENCES teams(id),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### CRUD Operations

**Create Walkthrough**
```typescript
const { data, error } = await supabase
  .from('walkthroughs')
  .insert({
    title: string,
    client_name: string,
    form_data: FormData,
    last_step: number,
    team_id: string
  })
  .select()
  .single();
```

**Read Walkthroughs**
```typescript
// Get all walkthroughs for user's team
const { data, error } = await supabase
  .from('walkthroughs')
  .select('*')
  .eq('team_id', teamId)
  .order('created_at', { ascending: false });

// Get specific walkthrough
const { data, error } = await supabase
  .from('walkthroughs')
  .select('*')
  .eq('id', walkthroughId)
  .single();
```

**Update Walkthrough**
```typescript
const { data, error } = await supabase
  .from('walkthroughs')
  .update({
    title: string,
    client_name: string,
    form_data: FormData,
    last_step: number,
    updated_at: new Date().toISOString()
  })
  .eq('id', walkthroughId)
  .select()
  .single();
```

**Delete Walkthrough**
```typescript
const { error } = await supabase
  .from('walkthroughs')
  .delete()
  .eq('id', walkthroughId);
```

### Product Catalog Table

#### Schema
```sql
CREATE TABLE product_catalog_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  sku_number TEXT,
  system_category TEXT NOT NULL,
  unit_cost DECIMAL NOT NULL,
  quantity INTEGER DEFAULT 1,
  unit TEXT DEFAULT 'piece',
  description TEXT,
  manufacturer TEXT,
  model TEXT,
  trade_price DECIMAL,
  msrp DECIMAL,
  room_type TEXT,
  subcategories TEXT[],
  discontinued BOOLEAN DEFAULT FALSE,
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Product Operations

**Create Product**
```typescript
const { data, error } = await supabase
  .from('product_catalog_items')
  .insert({
    name: string,
    sku_number: string,
    system_category: string,
    unit_cost: number,
    quantity: number,
    unit: string,
    description?: string,
    manufacturer?: string
  })
  .select()
  .single();
```

**Query Products**
```typescript
// Get products by category
const { data, error } = await supabase
  .from('product_catalog_items')
  .select('*')
  .eq('system_category', category)
  .eq('discontinued', false)
  .order('name');

// Search products
const { data, error } = await supabase
  .from('product_catalog_items')
  .select('*')
  .or(`name.ilike.%${searchTerm}%,sku_number.ilike.%${searchTerm}%`)
  .limit(50);
```

**Bulk Import Products**
```typescript
const { data, error } = await supabase
  .from('product_catalog_items')
  .insert(productArray)
  .select();
```

### User Profiles Table

#### Schema
```sql
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  company TEXT,
  title TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Profile Operations

**Get User Profile**
```typescript
const { data, error } = await supabase
  .from('profiles')
  .select('*')
  .eq('id', userId)
  .single();
```

**Update Profile**
```typescript
const { data, error } = await supabase
  .from('profiles')
  .update({
    first_name: string,
    last_name: string,
    company: string,
    title: string,
    phone: string,
    updated_at: new Date().toISOString()
  })
  .eq('id', userId)
  .select()
  .single();
```

## Local Storage API

### Walkthrough Service
For temporary data and offline capability:

```typescript
// Save walkthrough to localStorage
export const saveWalkthrough = async (title: string, formData: FormData): Promise<string | null> => {
  const id = `walkthrough-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const walkthrough: Walkthrough = {
    id,
    title,
    client_name: formData.clientInfo.fullName || "Unnamed Client",
    form_data: formData,
    last_step: formData.currentStep,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
  walkthroughs.push(walkthrough);
  localStorage.setItem('walkthroughs', JSON.stringify(walkthroughs));
  
  return id;
};

// Get walkthroughs from localStorage
export const getWalkthroughs = async (): Promise<Walkthrough[]> => {
  const walkthroughs = JSON.parse(localStorage.getItem('walkthroughs') || '[]');
  return walkthroughs;
};
```

### Configuration Storage

**Zapier Configuration**
```typescript
interface ZapierConfig {
  webhook: string;
  apiKey: string;
  zapId: string;
}

// Save configuration
export const saveZapierConfig = async (config: ZapierConfig): Promise<boolean> => {
  localStorage.setItem('zapierConfig', JSON.stringify(config));
  return true;
};

// Get configuration
export const getZapierConfig = async (): Promise<ZapierConfig> => {
  const storedConfig = localStorage.getItem('zapierConfig');
  return storedConfig ? JSON.parse(storedConfig) : {};
};
```

## External Integrations

### Zapier Webhook API

#### Send Data to Zapier
```typescript
export const sendToZapier = async (webhookUrl: string, payload: any): Promise<any> => {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
      mode: 'no-cors'
    });
    
    return { success: true };
  } catch (error) {
    console.error("Error sending data to Zapier:", error);
    throw error;
  }
};
```

#### Payload Structure
```typescript
interface ZapierPayload {
  customer_name: string;
  account_number: string;
  address: {
    line1: string;
    line2?: string;
    line3?: string;
    town: string;
    county: string;
    postcode: string;
    country: string;
  };
  phone_number: string;
  email_address: string;
  website_url?: string;
  notes: string;
  client_data: FormData;
}
```

### WeQuote Integration (Planned)

#### Configuration
```typescript
interface WeQuoteConfig {
  apiKey: string;
  baseUrl: string;
  username: string;
}
```

#### Template Import
```typescript
export const importWeQuoteTemplate = async (templateId: string): Promise<WeQuotePackageItem[]> => {
  const config = await getWeQuoteConfig();
  
  const response = await fetch(`${config.baseUrl}/templates/${templateId}`, {
    headers: {
      'Authorization': `Bearer ${config.apiKey}`,
      'Content-Type': 'application/json'
    }
  });
  
  const template = await response.json();
  return convertToPackageItems(template);
};
```

## Cost Calculation API

### Cost Structure Generation
```typescript
export const generateCostStructureFromRooms = (
  rooms: RoomConfig[], 
  systemCategories: SystemCategories,
  clientName: string
): CostStructure => {
  const costStructure: CostStructure = {
    id: `CS-${Math.floor(100000 + Math.random() * 900000)}`,
    name: `${clientName} Project Estimate`,
    description: `Generated from client walkthrough for ${clientName}`,
    taxRate: 8.25,
    generalMarkup: 10,
    createdAt: new Date(),
    updatedAt: new Date(),
    categories: []
  };
  
  // Generate categories and line items
  return costStructure;
};
```

### Cost Calculation
```typescript
export const calculateProjectCost = (
  selectedSystems: SystemCategories,
  qualityTier: string,
  squareFootage: number
): { totalCost: number; breakdown: Record<string, number> } => {
  const baseAmount = squareFootage * sqftMultipliers[tier];
  
  // Calculate system-specific costs
  const breakdown: Record<string, number> = {};
  let totalCost = 0;
  
  Object.entries(selectedSystems).forEach(([system, enabled]) => {
    if (enabled) {
      const systemCost = baseAmount * systemMultipliers[system];
      breakdown[system] = systemCost;
      totalCost += systemCost;
    }
  });
  
  return { totalCost, breakdown };
};
```

## File Upload API

### Supabase Storage
```typescript
// Upload file to Supabase Storage
export const uploadFile = async (file: File, bucket: string, path: string) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    });
    
  return { data, error };
};

// Get file URL
export const getFileUrl = (bucket: string, path: string) => {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);
    
  return data.publicUrl;
};
```

## PDF Generation API

### Report Generation
```typescript
export const generateWalkthroughPDF = async (formData: FormData): Promise<Uint8Array> => {
  const doc = new jsPDF();
  
  // Add client information
  doc.setFontSize(20);
  doc.text('Client Walkthrough Report', 20, 30);
  
  // Add client details
  doc.setFontSize(12);
  doc.text(`Client: ${formData.clientInfo.fullName}`, 20, 50);
  doc.text(`Email: ${formData.clientInfo.email}`, 20, 60);
  
  // Add room configurations
  formData.rooms.forEach((room, index) => {
    const yPos = 80 + (index * 20);
    doc.text(`Room: ${room.name}`, 20, yPos);
    // Add system details
  });
  
  return doc.output('arraybuffer');
};
```

## Error Handling

### API Error Types
```typescript
interface APIError {
  message: string;
  code?: string;
  details?: any;
}

// Supabase error handling
const handleSupabaseError = (error: any): APIError => {
  return {
    message: error.message || 'An unexpected error occurred',
    code: error.code,
    details: error.details
  };
};
```

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    timestamp: string;
  };
}
```

## Rate Limiting & Performance

### Query Optimization
- **Pagination**: Limit large result sets
- **Selective Fields**: Use `.select()` to fetch only needed columns
- **Indexing**: Database indexes on frequently queried columns
- **Caching**: TanStack Query for client-side caching

### Best Practices
- **Batch Operations**: Group multiple database operations
- **Connection Pooling**: Supabase handles connection management
- **Real-time Subscriptions**: Use sparingly for performance
- **File Size Limits**: Enforce reasonable upload limits

## Security Considerations

### Row Level Security (RLS)
```sql
-- Enable RLS on all tables
ALTER TABLE walkthroughs ENABLE ROW LEVEL SECURITY;

-- Team-based access policy
CREATE POLICY "team_access" ON walkthroughs
  FOR ALL USING (team_id = auth.jwt() ->> 'team_id');
```

### API Security
- **JWT Validation**: All requests validated through Supabase Auth
- **Input Sanitization**: Validate all user inputs
- **CORS Configuration**: Proper cross-origin request handling
- **Rate Limiting**: Prevent abuse through Supabase built-in limits
