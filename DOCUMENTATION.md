# ITS Discovery - Client Walkthrough Application

## Overview

ITS Discovery is a comprehensive web application designed for **Client Discovery and Project Walkthrough** in the home automation and smart systems industry. The application streamlines the process of conducting client consultations, capturing project requirements, generating cost estimates, and integrating with external systems.

## Core Purpose

The application serves as a digital tool for home automation consultants and integrators to:
- Conduct structured client walkthroughs
- Capture detailed project requirements
- Generate professional cost estimates
- Manage product catalogs and pricing
- Integrate with external systems (Zapier, WeQuote)
- Produce professional reports and documentation

## Key Features

### 🏠 **Client Walkthrough System**
- **Multi-step guided process** for capturing client information
- **Room-by-room system configuration** with detailed settings
- **System category selection** (Lighting, Audio, Video, Security, etc.)
- **File upload capabilities** for floor plans and reference materials
- **Real-time progress tracking** and form validation

### 💰 **Cost Management & Estimation**
- **Dynamic cost calculation** based on room configurations
- **Tiered pricing models** (Basic, Standard, Premium)
- **Labor and materials breakdown**
- **Tax and markup calculations**
- **Cost structure templates** and customization

### 📊 **Product Catalog Management**
- **Comprehensive product database** with SKU management
- **Category-based organization** by system types
- **Pricing and inventory tracking**
- **CSV import/export capabilities**
- **Manufacturer and model information**

### 🔗 **External Integrations**
- **Zapier webhook integration** for data synchronization
- **WeQuote template import** (planned feature)
- **Zoho CRM integration** capabilities
- **PDF generation and export**

### 📈 **Reporting & Analytics**
- **Professional PDF reports** generation
- **Project summaries and recommendations**
- **Cost breakdowns and proposals**
- **Client data export capabilities**

## Application Architecture

### Technology Stack
- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite 6.3.5
- **Styling**: Tailwind CSS + Radix UI Components
- **State Management**: React Context + TanStack Query
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **PDF Generation**: jsPDF + jsPDF-AutoTable
- **File Handling**: Native File API

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── categories/     # Product category management
│   ├── cost/           # Cost calculation components
│   ├── integrations/   # External service integrations
│   ├── products/       # Product catalog components
│   ├── rooms/          # Room configuration components
│   ├── steps/          # Walkthrough step components
│   └── ui/             # Base UI components (Radix)
├── context/            # React Context providers
├── hooks/              # Custom React hooks
├── integrations/       # External service configurations
├── pages/              # Main application pages
├── services/           # Business logic and API calls
├── types/              # TypeScript type definitions
└── utils/              # Utility functions
```

## Data Models

### Core Data Structures

#### FormData
```typescript
interface FormData {
  clientInfo: ClientInfo;           // Client contact information
  qualificationInfo: QualificationInfo; // Project requirements
  systemCategories: SystemCategories;   // Selected system types
  rooms: RoomConfig[];             // Room configurations
  currentStep: number;             // Current walkthrough step
}
```

#### ClientInfo
```typescript
interface ClientInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company?: string;
  address?: string;
  // Additional fields for compatibility
}
```

#### RoomConfig
```typescript
interface RoomConfig {
  id: string;
  name: string;
  systemSettings: RoomSystemSettings; // System configurations
  floor?: number | string;
  notes?: string;
  images?: string[];
}
```

#### SystemCategories
```typescript
interface SystemCategories {
  lighting: boolean;
  shades: boolean;
  audio: boolean;
  video: boolean;
  network: boolean;
  security: boolean;
  controlSystems: boolean;
}
```

### Database Schema (Supabase)

#### Tables
- **walkthroughs**: Stores client walkthrough data
- **product_catalog_items**: Product inventory and pricing
- **profiles**: User profile information
- **teams**: Team/organization management
- **cost_structures**: Saved cost estimates

## User Workflows

### 1. Client Walkthrough Process
1. **Client Information** - Capture basic client details
2. **Qualification** - Project budget, timeline, and requirements
3. **System Selection** - Choose applicable system categories
4. **Room Walkthrough** - Configure each room's systems
5. **File Upload** - Add floor plans and references
6. **Summary** - Review and validate all information
7. **Final Actions** - Generate reports and sync data

### 2. Cost Estimation Workflow
1. **Generate Structure** - Create cost structure from walkthrough
2. **Review Categories** - Validate system categories and rooms
3. **Adjust Pricing** - Modify costs, markup, and labor rates
4. **Calculate Totals** - Generate final pricing with tax
5. **Export Proposal** - Create PDF proposals for clients

### 3. Product Management Workflow
1. **Import Products** - CSV import with SKU validation
2. **Categorize Items** - Assign to system categories
3. **Set Pricing** - Configure costs, markup, and labor
4. **Manage Inventory** - Track quantities and availability

## Business Logic

### Cost Calculation Engine
The application uses a sophisticated cost calculation system:

```typescript
// Base calculation factors
const qualityTiers = {
  basic: { multiplier: 0.8, name: "Basic" },
  standard: { multiplier: 1.0, name: "Standard" },
  premium: { multiplier: 1.3, name: "Premium" }
};

// Square footage multipliers
const sqftMultipliers = {
  basic: 8,     // $8 per sq ft
  standard: 12, // $12 per sq ft
  premium: 18   // $18 per sq ft
};
```

### System Integration Logic
- **Zapier Integration**: Webhook-based data synchronization
- **WeQuote Import**: Template-based product import (planned)
- **PDF Generation**: Dynamic report creation with cost breakdowns

## Development Setup

### Prerequisites
- Node.js 18+ with npm
- Git for version control

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ITS-DISCO

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run build:dev    # Build for development
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Environment Configuration
The application uses Supabase for backend services. Configuration is handled through:
- `src/integrations/supabase/client.ts` - Supabase client setup
- Environment variables for API keys and URLs

## Security & Authentication

### Authentication System
- **Supabase Auth** with email/password and Google OAuth
- **Session management** with automatic token refresh
- **Protected routes** requiring authentication
- **User profile management** with team support

### Data Security
- **Row Level Security (RLS)** in Supabase
- **Team-based data isolation**
- **Secure API endpoints** with authentication
- **Client-side data validation**

## Integration Capabilities

### Zapier Integration
- **Webhook endpoints** for data synchronization
- **Customer data export** to external CRMs
- **Project data sharing** with team tools
- **Automated workflow triggers**

### WeQuote Integration (Planned)
- **Template import** from WeQuote system
- **Product catalog synchronization**
- **Pricing data integration**

## Deployment & Production

### Build Process
```bash
npm run build        # Creates optimized production build
npm run preview      # Test production build locally
```

### Production Considerations
- **Environment variables** for API keys
- **Database migrations** through Supabase
- **CDN deployment** for static assets
- **Performance monitoring** and error tracking

## Future Enhancements

### Planned Features
- **Mobile application** for field use
- **Advanced reporting** with analytics
- **Team collaboration** features
- **API integrations** with more vendors
- **Automated proposal generation**
- **Customer portal** for project tracking

### Technical Improvements
- **Performance optimization** for large datasets
- **Offline capability** for field work
- **Real-time collaboration** features
- **Advanced search** and filtering
- **Bulk operations** for data management
