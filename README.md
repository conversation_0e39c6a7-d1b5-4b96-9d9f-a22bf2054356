# ITS Discovery - Client Walkthrough Application

[![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-6.3.5-646CFF.svg)](https://vitejs.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-green.svg)](https://supabase.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4.11-38B2AC.svg)](https://tailwindcss.com/)

## 🏠 Overview

**ITS Discovery** is a comprehensive web application designed for **Client Discovery and Project Walkthrough** in the home automation and smart systems industry. The application streamlines the process of conducting client consultations, capturing project requirements, generating cost estimates, and integrating with external systems.

### Key Features

- 🎯 **Multi-step Client Walkthrough System** - Guided process for capturing detailed project requirements
- 🏠 **Room-by-Room Configuration** - Detailed system settings for each space
- 💰 **Dynamic Cost Calculation** - Real-time pricing based on selections and room configurations
- 📊 **Product Catalog Management** - Comprehensive inventory and pricing management
- 🔗 **External Integrations** - Zapier webhooks, WeQuote templates, and CRM synchronization
- 📈 **Professional Reporting** - PDF generation and project documentation
- 👥 **Team Collaboration** - Multi-user support with role-based access

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.0.0 or higher
- **npm** 8.0.0 or higher
- **Git** for version control

### Installation

```bash
# Clone the repository
git clone https://github.com/ITS-DEV-LG/ITS-DISCO.git
cd ITS-DISCO

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:8080`

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run build:dev    # Build for development
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

## 📚 Documentation

Comprehensive documentation is available in the following files:

- **[DOCUMENTATION.md](./DOCUMENTATION.md)** - Complete application functionality and architecture overview
- **[DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)** - Detailed development setup, workflows, and best practices
- **[API_DOCUMENTATION.md](./API_DOCUMENTATION.md)** - API endpoints, data structures, and integration patterns

## 🏗️ Technology Stack

### Frontend
- **React 18.3.1** - Modern React with hooks and concurrent features
- **TypeScript 5.5.3** - Type safety and enhanced developer experience
- **Vite 6.3.5** - Fast build tool and development server
- **Tailwind CSS 3.4.11** - Utility-first CSS framework
- **Radix UI** - Accessible, unstyled UI components

### Backend & Database
- **Supabase** - PostgreSQL database with real-time features
- **Supabase Auth** - Authentication and user management
- **Row Level Security** - Database-level security

### State Management
- **React Context** - Global state management
- **TanStack Query 5.56.2** - Server state management and caching
- **React Hook Form 7.53.0** - Form state management

## 🎯 Core Workflows

### 1. Client Walkthrough Process
1. **Client Information** - Capture basic client details
2. **Qualification** - Project budget, timeline, and requirements
3. **System Selection** - Choose applicable system categories
4. **Room Walkthrough** - Configure each room's systems
5. **File Upload** - Add floor plans and references
6. **Summary** - Review and validate all information
7. **Final Actions** - Generate reports and sync data

### 2. Cost Estimation
- Dynamic cost calculation based on room configurations
- Tiered pricing models (Basic, Standard, Premium)
- Labor and materials breakdown
- Tax and markup calculations
- Professional PDF proposals

### 3. Product Management
- CSV import/export capabilities
- Category-based organization
- Pricing and inventory tracking
- Manufacturer and model information

## 🔗 Integrations

### Zapier Integration
- Webhook-based data synchronization
- Customer data export to external CRMs
- Project data sharing with team tools
- Automated workflow triggers

### WeQuote Integration (Planned)
- Template import from WeQuote system
- Product catalog synchronization
- Pricing data integration

## 🗃️ Database Schema

### Core Tables
- **walkthroughs** - Client walkthrough data and form states
- **product_catalog_items** - Product inventory and pricing
- **profiles** - User profile information
- **teams** - Team/organization management
- **cost_structures** - Saved cost estimates

## 🔒 Security Features

- **Supabase Authentication** with email/password and Google OAuth
- **Row Level Security (RLS)** for data isolation
- **Team-based access control**
- **Secure API endpoints** with JWT validation
- **Input validation** and sanitization

## 🚀 Deployment

### Build Process
```bash
npm run build        # Creates optimized production build
npm run preview      # Test production build locally
```

### Deployment Options
- **Vercel** (Recommended)
- **Netlify**
- **AWS S3 + CloudFront**

## 🤝 Contributing

We welcome contributions! Please see our [Development Guide](./DEVELOPMENT_GUIDE.md) for detailed information on:

- Setting up the development environment
- Code standards and best practices
- Pull request process
- Testing guidelines

### Development Workflow
1. Fork the repository
2. Create a feature branch from `development`
3. Make your changes with proper testing
4. Submit a pull request with clear description
5. Code review and approval process

## 📄 License

This project is proprietary software developed by Innovative Technology Solutions (ITS).

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **GitHub Issues**: [Create an issue](https://github.com/ITS-DEV-LG/ITS-DISCO/issues)

## 🔄 Recent Updates

### Latest Security Updates (January 2025)
- ✅ **All security vulnerabilities resolved** - Updated to Vite 6.3.5 and esbuild 0.25.5
- ✅ **Zero vulnerabilities** confirmed via npm audit
- ✅ **Full functionality verified** - All features tested and working
- ✅ **Performance optimized** - Build process and development server running smoothly

### Version History
- **v1.0.0** - Initial release with core walkthrough functionality
- **v1.1.0** - Added cost calculation and PDF generation
- **v1.2.0** - Integrated Zapier webhooks and external APIs
- **v1.3.0** - Enhanced product catalog and team features
- **v1.4.0** - Security updates and performance improvements
